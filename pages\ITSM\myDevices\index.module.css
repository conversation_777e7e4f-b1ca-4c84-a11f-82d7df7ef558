/* Top Container Styles */
.myDevicesTopContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 1rem;
}

.backArrow {
  cursor: pointer;
  width: 45px;
  height: 45px;
  transition: opacity 0.2s ease;
}

.backArrow:hover {
  opacity: 0.7;
}

.deviceActionsContainer {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-left: auto;
}

.searchContainer {
  position: relative;
}

/* Table Styles */
.tableHeader {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  background-color: #1b84ff0d;
  color: #515151;
  padding: 1rem;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
}

.searchIcon {
  color: #666666 !important;
  font-size: 1rem;
  margin-left: 0.8rem;
}

.searchInput {
  padding: 8px 12px 8px 35px !important;
  border: 1px solid #ddd !important;
  border-radius: 6px !important;
  font-size: 0.9rem !important;
  width: 250px;
  background-color: white !important;
}

.searchInput:focus {
  border-color: #024F7C !important;
  box-shadow: 0 0 0 2px rgba(2, 79, 124, 0.1) !important;
}

/* Action Column Styles */
.actionButtons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.actionIcon {
  cursor: pointer;
  font-size: 1rem;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewIcon {
  color: #007BFF;
  background-color: rgba(0, 123, 255, 0.1);
}

.viewIcon:hover {
  background-color: rgba(0, 123, 255, 0.2);
  color: #0056b3;
}

.editIcon {
  color: #007BFF;
  background-color: rgba(0, 123, 255, 0.1);
}

.editIcon:hover {
  background-color: rgba(0, 123, 255, 0.2);
  color: #0056b3;
}

/* Supplier ID Link Styles */
.supplierIdLink {
  color: #007BFF;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.supplierIdLink:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Condition Badge Styles */
.conditionBadge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
  min-width: 50px;
}

.newCondition {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28A745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.usedCondition {
  background-color: rgba(255, 193, 7, 0.1);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Device Detailed View Styles */

.ticketTitle {
  color: #012853;
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.2;
}

.deviceDetailedViewContainer {
  margin-top: 10px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.tabContainer {
  display: flex;
  width: 30%;
}

.tab {
  padding: 10px 20px;
  border: 1px solid #333;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  flex: 1;
  background-color: #98A6BA;
  color: white;
}

.active {
  background-color: #024F7C;
}

.topContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 1rem;
}

.middleContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

 /* Asset Overview Styles */
.activeTabContainer {
  flex: 1;
  align-self: flex-start;
}

 .assetOverviewContainer {
   display: flex;
   gap: 40px;
   padding: 20px;
 }

 .assetInfoColumn {
   flex: 1;
   display: flex;
   flex-direction: column;
   gap: 20px;
 }

 .assetInfoItem {
   display: flex;
   flex-direction: column;
   gap: 5px;
 }

 .assetInfoTitle {
   color: #024F7C;
   font-weight: 600;
   font-size: 0.85rem;
 }

 .assetInfoValue {
   color: black;
   font-size: 0.85rem;
   font-weight: 500;
 }