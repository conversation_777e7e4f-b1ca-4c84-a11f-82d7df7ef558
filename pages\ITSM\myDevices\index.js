import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import { useRouter } from 'next/router'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useState, useContext } from 'react'
import { InputText } from 'primereact/inputtext'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import Image from 'next/image'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Button from '../../../components/UI/Button/Button'
import clsx from 'clsx'

import Backarrow from '../../../svg/metronic/back_metronic.svg'
import RequestDeviceIcon from '../../../images/itsm_request_new_device_icon.png'

import styles from './index.module.css'

export default function MyDevices() {
  const router = useRouter()
  const userProfile = useContext(UserProfileContext)
  const [globalFilter, setGlobalFilter] = useState('')

  return (
    <PageContainer theme="metronic">
      <div className={styles.myDevicesTopContainer}>
        <Image src={Backarrow} alt="Back" onClick={() => router.back()} className={styles.backArrow} />
        <BreadCrumbs title="My Devices" breadcrumbItems={[{ label: 'Dashboard' }]} theme="metronic" />
        <div className={styles.deviceActionsContainer}>
          <Button
            label="Request New Device"
            theme="metronic"
            width="235px"
            icon={<Image src={RequestDeviceIcon} alt="Request Device" />}
            onClick={() => console.log('Request new device')}
          />
          <div className={styles.searchContainer}>
            <span className="p-input-icon-left">
              <i className={clsx('pi pi-search', styles.searchIcon)} />
              <InputText
                placeholder="Search"
                className={styles.searchInput}
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
              />
            </span>
          </div>
        </div>
      </div>

      <DataTable value={deviceRows} className="custom-lead" header={tableHeader} globalFilter={globalFilter}>
        <Column field="action" header="Action" body={actionTemplate} headerStyle={{ width: '8%' }} />
        <Column field="assetId" header="Asset ID" headerStyle={{ width: '8%' }} />
        <Column field="itemId" header="Item ID" headerStyle={{ width: '8%' }} />
        <Column field="assetName" header="Asset Name" headerStyle={{ width: '15%' }} />
        <Column field="category" header="Category" headerStyle={{ width: '10%' }} />
        <Column field="serialNumber" header="Serial #" headerStyle={{ width: '10%' }} />
        <Column field="supplierId" header="Supplier ID" body={supplierIdTemplate} headerStyle={{ width: '10%' }} />
        <Column field="supplierName" header="Supplier Name" headerStyle={{ width: '12%' }} />
        <Column field="assetCondition" header="Asset Condition" body={conditionTemplate} headerStyle={{ width: '10%' }} />
        <Column field="dateAssigned" header="Date Assigned" headerStyle={{ width: '10%' }} />
        <Column field="lastUpdated" header="Last Updated" headerStyle={{ width: '10%' }} />
      </DataTable>
    </PageContainer>
  )
}

// ========== MOCK DATA ==========

const deviceRows = [
  {
    assetId: '0001',
    itemId: 'SAM-1001',
    assetName: '65" TV',
    category: 'Media',
    serialNumber: '100000',
    supplierId: 'SAM123',
    supplierName: 'Samsung',
    assetCondition: 'New',
    dateAssigned: '07/04/25',
    lastUpdated: '07/04/25'
  },
  {
    assetId: '0002',
    itemId: 'SAM-1001',
    assetName: '65" TV',
    category: 'Media',
    serialNumber: '100001',
    supplierId: 'SAM123',
    supplierName: 'Samsung',
    assetCondition: 'New',
    dateAssigned: '07/02/25',
    lastUpdated: '07/02/25'
  },
  {
    assetId: '0003',
    itemId: 'HP-1000',
    assetName: 'Laptop',
    category: 'Media',
    serialNumber: '219487',
    supplierId: 'DEL521',
    supplierName: 'Dell',
    assetCondition: 'Used',
    dateAssigned: '07/02/25',
    lastUpdated: '07/02/25'
  },
  {
    assetId: '0004',
    itemId: 'SO-1234',
    assetName: '2 Seater Sofa',
    category: 'Furniture',
    serialNumber: '321312',
    supplierId: 'RCW442',
    supplierName: 'RC Wiley',
    assetCondition: 'Used',
    dateAssigned: '07/01/25',
    lastUpdated: '07/01/25'
  },
  {
    assetId: '0005',
    itemId: 'SO-1234',
    assetName: '2 Seater Sofa',
    category: 'Furniture',
    serialNumber: '421421',
    supplierId: 'RCW442',
    supplierName: 'RC Wiley',
    assetCondition: 'New',
    dateAssigned: '07/01/25',
    lastUpdated: '07/01/25'
  }
]

// ========== TEMPLATES ==========

const actionTemplate = (rowData) => (
  <div className={styles.actionButtons}>
    <i className={clsx('pi pi-eye', styles.actionIcon, styles.viewIcon)} title="View" />
    <i className={clsx('pi pi-pencil', styles.actionIcon, styles.editIcon)} title="Edit" />
  </div>
)

const supplierIdTemplate = (rowData) => <span className={styles.supplierIdLink}>{rowData.supplierId}</span>

const conditionTemplate = (rowData) => {
  const isNew = rowData.assetCondition === 'New'
  return <div className={clsx(styles.conditionBadge, isNew ? styles.newCondition : styles.usedCondition)}>{rowData.assetCondition}</div>
}

const tableHeader = (
  <div className={styles.tableHeader}>
    <span className="text-xl font-bold m-2">My Devices</span>
  </div>
)
