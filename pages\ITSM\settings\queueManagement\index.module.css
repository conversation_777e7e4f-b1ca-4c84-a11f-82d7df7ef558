.breadcrumbsContainer {
  margin-bottom: 20px;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.backArrow {
  cursor: pointer;
  width: 45px;
  height: 45px;
  transition: opacity 0.2s ease;
}

.backArrow:hover {
  opacity: 0.7;
}

.headerContainer {
  margin-bottom: 10px;
}

.queueManagementHeader {
  color: #012853;
  font-size: 0.85rem;
  font-weight: 600;
  background-color: rgba(0, 157, 255, 0.07);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.queueManagementHeader h2 {
  margin: 0;
  padding: 10px;
}

.queueManagementHeaderUnderline {
  width: 100%;
  height: 3px;
  background-color: #012853;
}

.queueManagementTableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #1b84ff0d;
  color: #515151;
  padding: 1rem;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
}

.modalContent {
  padding: 0;
}

.modalInstructions {
  margin-bottom: 20px;
  color: #012853;
  font-size: 0.95rem;
  line-height: 1.5;
}

.modalSection {
  margin-bottom: 20px;
}

.modalSectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin: 0 0 15px 0;
}

.modalSectionButtons {
  display: flex;
  justify-content: flex-end;
}

/* Configuration Form Styles */

.configurationSection {
  margin: 0 0 10px 0;
}

.configurationInstructions {
  margin: 10px 0;
  color: #012853;
}

.configurationNote {
  margin-bottom: 20px;
  color: #515151;
  font-size: 0.9rem;
  line-height: 1.5;
  font-style: italic;
}

.configurationFormGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.configurationFormRow {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.configurationFormRow .configurationFormGroup {
  flex: 1;
  margin-bottom: 0;
}

.configurationLabel {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin-bottom: 5px;
  padding-left: 5px;
}

.configurationSectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin: 0 0 10px 0;
}

.configurationMetadataRow {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.configurationMetadataGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: 150px;
}

.configurationMetadataLabel {
  font-size: 0.9rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
}

.configurationMetadataValue {
  font-size: 0.9rem;
  color: #515151;
  font-family: 'Open Sans';
}

.toggleItem {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.technicianColumnsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.technicianColumnsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  min-height: 200px;
}

.technicianColumn {
  border: 2px dashed #e5e5e5;
  border-radius: 12px;
  padding: 15px;
  background-color: #fafafa;
  min-height: 150px;
}

.technicianColumnHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
}

.technicianColumnTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin: 0;
}

.addTechnicianButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: 1px solid #1b84ff;
  color: #1b84ff;
  padding: 5px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addTechnicianButton:hover {
  background-color: #1b84ff0d;
}

.technicianColumnContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.technicianItem {
  background-color: white;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 10px;
  transition: all 0.2s ease;
}

.technicianItem:hover {
  border-color: #1b84ff;
  box-shadow: 0 2px 4px rgba(27, 132, 255, 0.1);
}

.technicianItemHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.technicianItemAction {
  display: flex;
  align-items: center;
}

.technicianSelectContainer {
  width: 100%;
}

.addNewUserValueButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  border: 2px dashed #1b84ff;
  color: #1b84ff;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Open Sans';
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.addNewUserValueButton:hover {
  background-color: #1b84ff0d;
}

.deleteIcon {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.deleteIcon:hover {
  opacity: 0.7;
}

.dragHandleContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
  background-color: #E5E5E5;
  border-radius: 4px;
  touch-action: none;
  padding: 15px 15px;
  cursor: move;
}

.orderContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.orderLabel {
  font-size: 1rem;
  color: #515151;
  font-family: 'Open Sans';
}

.orderNumber {
  font-size: 1rem;
  color: #015187;
  font-family: 'Open Sans';
  font-weight: 600;
  background-color: #E5E5E5;
  border-radius: 10px;
  padding: 5px 10px;
}