import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { RelationshipsActionTemplate } from '../UI/RelationshipsActionTemplate/RelationshipsActionTemplate'
import { RelationshipsNameTemplate } from '../UI/RelationshipsNameTemplate/RelationshipsNameTemplate'
import { useRef, useState, useContext } from 'react'
import Modal from '../../Modal/Modal'
import { CreateRelationship } from '../../Relationship/Relationship'
import { getAccessTokenForScopeSilent } from '../../../../src/GetAccessTokenForScopeSilent'
import { formBuilderApiRequest } from '../../../../src/msalConfig'
import { useQuery } from '@tanstack/react-query'
import { Toast } from 'primereact/toast'
import { BoolColumnTemplate } from '../UI/BoolColumnTemplate/BoolColumnTemplate'
import { DateColumnTemplate } from '../UI/DateColumnTemplate/DateColumnTemplate'
import { RelationTypeColumnTemplate } from '../UI/RelationTypeColumnTemplate/RelationTypeColumnTemplate'
import { ConditionalDisplay } from '../../ConditionalDisplay/ConditionalDisplay'
import UserProfileContext from '../../../../public/UserProfileContext/UserProfileContext'

import styles from './RelationshipsDashboard.module.css'
import { re } from 'mathjs'
import Button from '../../Button/Button'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

// TODO: Add tableHeader to the RelationshipsDashboard component for metronic theme.
export const RelationshipsDashboard = ({ formSubmission, eSignRequest, dmsFile, isLatestDmsFileVersion, theme }) => {
  const [isButtonClicked, setIsButtonClicked] = useState(false)
  const toast = useRef(null)
  const userProfile = useContext(UserProfileContext)
  const role = userProfile?.role?.name

  const isAllowedRole = (role) => ['Admin', 'Contributor'].includes(role)
  const condition = isAllowedRole(role)

  const ensureCurrentObjectFirst = (relationEntities) => {
    if (dmsFile && dmsFile.id) {
      if (relationEntities[0].revDriveFileId && relationEntities[0].revDriveFileId === dmsFile.id) {
        // dmsFile is already at index 0, no further action needed
        return
      } else {
        // Find dmsFile in the array and swap it to index 0
        const dmsIndex = relationEntities.findIndex((entity) => entity.revDriveFileId === dmsFile.id)
        if (dmsIndex > 0) {
          ;[relationEntities[0], relationEntities[dmsIndex]] = [relationEntities[dmsIndex], relationEntities[0]]
        }
      }
    }

    // Check if the first relationEntity corresponds to the esignRequest
    if (eSignRequest && eSignRequest.eSignRequestId) {
      if (relationEntities[0].eSignRequestId && relationEntities[0].eSignRequestId === eSignRequest.eSignRequestId) {
        // eSignRequest is already at index 0, no further action needed
        return
      } else {
        // Find esignRequest in the array and swap it to index 0
        const eSignIndex = relationEntities.findIndex((entity) => entity.eSignRequestId === eSignRequest.eSignRequestId)
        if (eSignIndex > 0) {
          ;[relationEntities[0], relationEntities[eSignIndex]] = [relationEntities[eSignIndex], relationEntities[0]]
        }
      }
    }

    // Check if the first relationEntity corresponds to the formSubmission
    if (formSubmission && formSubmission.id) {
      if (relationEntities[0].formSubmissionId && relationEntities[0].formSubmissionId === formSubmission.id) {
        // formSubmission is already at index 0, no further action needed
        return
      } else {
        // Find formSubmission in the array and swap it to index 0
        const formIndex = relationEntities.findIndex((entity) => entity.formSubmissionId === formSubmission.id)
        if (formIndex > 0) {
          ;[relationEntities[0], relationEntities[formIndex]] = [relationEntities[formIndex], relationEntities[0]]
        }
      }
    }
  }

  const fetchRelationships = async () => {
    const getApiUrl = () => {
      if (formSubmission?.id) {
        return `${api}Relationship/FormSubmission/${formSubmission?.id}`
      } else if (eSignRequest?.eSignRequestId) {
        return `${api}Relationship/ESignRequest/${eSignRequest?.eSignRequestId}`
      } else if (dmsFile?.id) {
        return `${api}Relationship/DmsDocument/${dmsFile?.id}`
      }
    }

    try {
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
      const apiUrl = getApiUrl()

      const response = await fetch(apiUrl, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`)
      }

      const data = await response.json()

      data.forEach((relationship) => {
        const { relationEntities } = relationship
        ensureCurrentObjectFirst(relationEntities)
      })

      return data
    } catch (error) {
      console.error('Error fetching relationships', error)
      return []
    }
  }

  const generateQueryKey = () => {
    if (formSubmission && formSubmission.id) {
      return ['relationships', 'formsubmission', formSubmission.id]
    } else if (eSignRequest && eSignRequest.eSignRequestId) {
      return ['relationships', 'esignRequest', eSignRequest.eSignRequestId]
    } else if (dmsFile && dmsFile.id) {
      return ['relationships', 'dmsFile', dmsFile.id]
    }

    return []
  }

  const isQueryEnabled = () => {
    return !!(formSubmission?.id || eSignRequest?.eSignRequestId || dmsFile?.id)
  }

  const {
    data: relationships,
    isLoading: loadingRelationships,
    refetch: refetchRelationships
  } = useQuery({
    queryKey: generateQueryKey(),
    queryFn: fetchRelationships,
    staleTime: 0,
    enabled: isQueryEnabled()
  })

  const handleRelationCreation = (isSuccessful) => {
    if (isSuccessful) {
      refetchRelationships()
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Related Item Created',
        life: 2500
      })
    } else if (!isSuccessful) {
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Related Item Creation Failed',
        life: 2500
      })
    }

    setIsButtonClicked(false)
  }

  const getCurrentRelationEntity = () => {
    if (formSubmission && formSubmission.id) {
      return formSubmission
    } else if (eSignRequest && eSignRequest.eSignRequestId) {
      return eSignRequest
    } else if (dmsFile && dmsFile.id) {
      return dmsFile
    }
  }

  return (
    <>
      <Toast ref={toast} />
      <Modal header={' Add Related Items'} visible={isButtonClicked} onHide={() => setIsButtonClicked(false)}>
        <CreateRelationship
          currentRelationships={relationships}
          currentRelationEntity={getCurrentRelationEntity()}
          handleCreation={handleRelationCreation}
        />
      </Modal>
      <div className={styles.relationshipsTitleSpace}>
        <div className={styles.relationshipsDashboardLabel}>Related Items</div>
        <ConditionalDisplay condition={isAllowedRole(role) && (!dmsFile || isLatestDmsFileVersion)}>
          <Button
            label="Add Related Item"
            onClick={() => setIsButtonClicked((prev) => !prev)}
            icon={<i className="pi pi-plus"></i>}
            width="13rem"
          ></Button>
        </ConditionalDisplay>
      </div>
      <div key={formSubmission?.id ?? eSignRequest?.eSignRequestId ?? 0}>
        <DataTable
          value={relationships}
          loading={loadingRelationships}
          sortField={'lastUpdatedAtUtc'}
          sortOrder={-1}
          size="small"
          className={theme === 'metronic' ? 'custom-lead' : 'p-datatable-audit-history'}
        >
          <Column field="id" header="ID" headerStyle={{ width: '4%' }} sortable pt={{ bodyCell: 'px-2', headerCell: 'px-2' }} />
          <Column
            field="relationType"
            header="Type"
            body={(rowData) => <RelationTypeColumnTemplate relationEntity={rowData?.relationEntities} />}
            headerStyle={{ width: '11%' }}
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />

          <Column
            field="relationEntities"
            header="Name"
            body={(rowData) => {
              return <RelationshipsNameTemplate relationEntity={rowData?.relationEntities} />
            }}
            headerStyle={{ width: '12%' }}
            pt={{ bodyCell: 'px-0', headerCell: 'px-2' }}
          />
          <Column
            field="isActive"
            header="Active"
            body={(rowData) => <BoolColumnTemplate bool={rowData?.isActive ?? false} trueLabel={'Active'} falseLabel={'Not Active'} />}
            headerStyle={{ width: '5%' }}
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          <Column
            field="relationshipTypeName"
            header="Related Item"
            headerStyle={{ width: '10%' }}
            sortable
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          <Column
            field="comment"
            header="Comment"
            headerStyle={{ width: '10%' }}
            body={(rowData) => {
              return (
                <div
                  className="overflow-hidden text-overflow-ellipsis white-space-nowrap w-15rem"
                  title={rowData.comment ? rowData.comment : 'NA'}
                >
                  {rowData.comment ? rowData.comment : 'NA'}
                </div>
              )
            }}
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          <Column
            field="createdByDisplayName"
            header="Created By"
            headerStyle={{ width: '11%' }}
            sortable
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          <Column
            field="createdAtUtc"
            header="Created On"
            body={(rowData) => <DateColumnTemplate date={rowData?.createdAtUtc} />}
            headerStyle={{ width: '10%' }}
            sortable
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          <Column
            field="updatedByDisplayName"
            header="Updated By"
            headerStyle={{ width: '11%' }}
            sortable
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          <Column
            field="lastUpdatedAtUtc"
            header="Updated On"
            body={(rowData) => <DateColumnTemplate date={rowData?.lastUpdatedAtUtc} />}
            headerStyle={{ width: '10%' }}
            sortable
            pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
          />
          {condition && (
            <Column
              field="action"
              header="Action"
              body={(rowData) => (
                <RelationshipsActionTemplate
                  currentRelationEntity={getCurrentRelationEntity()}
                  rowData={rowData}
                  toast={toast}
                  refetchRelationships={refetchRelationships}
                />
              )}
              headerStyle={{ width: '5%' }}
              pt={{ bodyCell: 'px-2', headerCell: 'px-2' }}
            />
          )}
        </DataTable>
      </div>
    </>
  )
}
