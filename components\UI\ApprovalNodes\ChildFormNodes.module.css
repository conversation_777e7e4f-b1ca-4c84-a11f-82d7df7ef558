.pillContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0;
}

.pillInactive {
  background: white;
  color: #666;
  border: 1px solid #e0e0e0;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 38px;
}

.pillActive {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 38px;
}

.pillApproved {
  background: #28a745;
  color: white;
}

.pillRejected {
  background: #dc3545;
  color: white;
}

.pillInvisible {
  opacity: 0;
}

.dottedConnector {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  border-left: 2px dotted #ccc;
}

.initialDottedConnector {
  width: 2px;
  height: 10px;
  border-left: 2px dotted #ccc;
}

.defaultPills {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 0;
  width: 240px;
  align-items: center;
}

.cardContainer {
  width: 240px;
  min-height: 100px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.cardContainerCyanStrip {
  width: 100%;
  height: 4px;
  background: #00b9ff;
  flex-shrink: 0;
}

.cardContent {
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
}

.formTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.2;
  text-align: center;
}

.lineId {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  text-align: center;
}

.currentApproverOuterContainer {
  font-size: 13px;
  color: #555;
  margin-bottom: 3px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.currentApproverInnerContainer {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  flex-shrink: 0;
}

.lastUpdatedDate {
  font-size: 11px;
  color: #777;
  margin-bottom: 4px;
  text-align: center;
}
