.myTicketsTopContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem; 
}

.newTicketContainer {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.newTicketContainer > span {
  display: flex;
  flex-direction: row-reverse;
}

.dashboardGridContainer {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.dashboardColumn {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.dashboardColumn:first-child {
  flex: 1.5;
}

.searchIcon {
  color: #51515180;
  font-size: 18px;
  right: 16px;
  position: absolute;
  display: flex;
  align-items: center;
  left: auto;
  cursor: pointer;
}

.search {
  flex: 1;
  border: 1px solid var(--primary-bg-darkCerulean-10) !important;
  border-radius: 10px;
  padding: 8px 12px;
  font-size: 15px;
  outline: none;
  background: var(--background-light);
  color: #515151;
  height: 45px;
  width: 16rem;
}

/* First Row Container */
.firstRowContainer {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  align-items: flex-start;
}

/* Profile Card */
.profileCard {
  background: #ffffff;
  border: 1px solid rgba(81, 81, 81, 0.1);
  box-shadow: 3px 3px 10px 1px rgba(27, 132, 255, 0.05);
  border-radius: 20px;
  padding: 15px;
  display: flex;
  gap: 1.5rem;
  flex: 1;
  transition: box-shadow 0.2s;
  max-height: 160px;
}

.profileCard:hover {
  box-shadow: 3px 3px 15px 1px rgba(27, 132, 255, 0.1);
}

.profileImage {
  display: flex;
  align-items: center;
}

.profileImagePlaceholder {
  font-size: 3rem;
  color: #ccc;
}

.avatarPlaceholder {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: #f8f9fa;
  border: 2px solid rgba(81, 81, 81, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.profileDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.profileHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.userName {
  color: #515151;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.editIcon {
  padding: 0.5rem;
  margin-bottom: -40px;
  border-radius: 8px;
  transition: background-color 0.2s;
  align-self: flex-end;
}

.editIcon:hover {
  background-color: rgba(27, 132, 255, 0.1);
}

.profileInfo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.infoRow {
  display: flex;
  gap: 0.25rem;
}

.label {
  color: #515151;
  font-size: 0.85rem;
  font-weight: 600;
}

.value {
  color: #1b84ff;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Summary Cards */
.summaryCards {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.summaryCard {
  background: #ffffff;
  border: 1px solid rgba(81, 81, 81, 0.1);
  box-shadow: 3px 3px 10px 1px rgba(27, 132, 255, 0.05);
  border-radius: 20px;
  padding: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  gap: 1rem;
  width: 100%;
  max-width: 235px;
  min-height: 160px;
  transition: box-shadow 0.2s;

}

.summaryCard:hover {
  box-shadow: 3px 3px 15px 1px rgba(27, 132, 255, 0.1);
}

.cardIcon {
  width: 90px;
  height: 90px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.cardContent {
  flex: 1;
}

.cardLabel {
  color: #515151;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.cardValue {
  color: #515151;
  font-size: 1.25rem;
  font-weight: 700;
}

.editUserPencilIcon {
  background-color: #1b84ff;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 25%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Second Row Container */
.secondRowContainer {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}

/* Panel Base Styles */
.programStatusTracker,
.importantNews,
.latestUpdates {
  background: #ffffff;
  border: 1px solid rgba(81, 81, 81, 0.1);
  box-shadow: 3px 3px 10px 1px rgba(27, 132, 255, 0.05);
  border-radius: 20px;
  padding: 20px;
  transition: box-shadow 0.2s;
  max-height: 240px;
}

.programStatusTracker {
  flex: 2;
}

.importantNews,
.latestUpdates {
  flex: 1;
}

.latestUpdates {
  flex: 1.5;
}

.programStatusTracker:hover,
.importantNews:hover,
.latestUpdates:hover {
  box-shadow: 3px 3px 15px 1px rgba(27, 132, 255, 0.1);
}

.programStatusTracker h2,
.importantNews h2,
.latestUpdates h2 {
  color: #515151;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
}

/* Program Status Tracker Styles */
.statusGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

/* Custom scrollbar styling */
.statusGrid::-webkit-scrollbar {
  width: 6px;
}

.statusGrid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.statusGrid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.statusGrid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.statusItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  min-width: 100px;
  flex: 1;
}

.statusContainer {
  width: 29px;
  height: 29px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.statusLabel {
  color: grey;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.statusOnline {
  background-color: #e8f5e8;
  color: #2e7d32;
  text-align: center;
}

.statusMaintenance {
  background-color: #ffebee;
  color: #c62828;
  text-align: center;
}

/* Important News Styles */
.newsItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(81, 81, 81, 0.08);
  border-radius: 25px;
  margin-bottom: 0.75rem;
}

.newsItem:last-child {
  margin-bottom: 0;
}

.newsIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.newsContent {
  flex: 1;
}

.newsContainer {
  overflow: scroll;
  height: 150px;
}

.newsTitle {
  color: #515151;
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.newsDate {
  color: #888;
  font-size: 0.75rem;
  margin: 0;
}

.newsArrow {
  color: #ccc;
  font-size: 1rem;
}

/* Latest Updates Styles */
.updateItem {
  background-color: rgba(81, 81, 81, 0.08);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.updateItem:last-child {
  margin-bottom: 0;
  overflow: scroll;
  height: 150px;
}

.updateHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.updateHeader > span {
  font-size: 1rem;
  font-weight: 600;
  color: #515151;
}

.updateIcon {
  background: #ffa726;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.updateStatus {
  color: #515151;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.updateTitle {
  color: #515151;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.updateDescription {
  color: #515151;
  font-size: 1.1rem;
  line-height: 1.4;
  margin: 0 0 1rem 0;

}

.updateButton {
  background: #1b84ff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.updateButton:hover {
  background: rgba(90, 98, 104, 0.9);
}

/* Status Column Styles */
.approveContainer {
  background-color: #eafff1;
  border: 1px solid #04cb12;
  color: #515151;
}

.approveCircle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #04cb12;
}

.inprogressContainer {
  background-color: #fffdef;
  border: 1px solid #ffde05;
  color: #515151;
}

.inprogressCircle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ffde05;
}

/* Template Column Styles */
.statusBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 1.5rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.userProfileContainer {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.avatarBase {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
}

.avatarAssigned {
  background-color: #6c757d;
}

.avatarUpdated {
  background-color: #1b84ff;
}

/* .userName {
  font-size: 0.875rem;
  color: #515151;
} */

.tableHeader {
  background-color: #1b84ff0d;
  color: #515151;
  padding: 1rem;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.submitTicketContainer {
  padding-top: 2rem;
}

.submitTicketContainer .tableHeader {
  font-size: 1.5rem;
  font-weight: 600;
  justify-content: center;
}

.submitTicketBox {
  background: #fff;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.ticketHeaderContent {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
}

.ticketImageWrapper {
  flex-shrink: 0;
}

.ticketTextContent {
  max-width: 700px;
}

.ticketTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #1a202c;
}

.ticketDescription {
  font-size: 1rem;
  margin-bottom: 2rem;
  color: #4a5568;
}

.ticketTips {
  font-size: 0.9rem;
  color: #2563eb;
}

.ticketTipsDescription {
  margin-top: 1rem;
}

.submitTicketInfoContainer {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 1rem;
}

.submitTicketInfoContainer > div {
  width: 25%;
}

.submitTicketInfoContainer > div > label,
.submitTicketIssueDescriptionContainer > div > label {
  font-size: 1rem;
  font-weight: 600;
  color: #515151;
}

.submitTicketFormBox {
  padding: 2rem;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.submitTicketIssueDescriptionContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  padding: 1rem;
}

.submitTicketIssueDescriptionContainer > div {
  width: 51.5%;
  align-self: center;
}

.inputWithIcon {
  position: relative;
}

.inputIcon {
  position: absolute;
  top: 50%;
  left: 5px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  pointer-events: none;
  z-index: 1;
}

.inputWithPadding {
  padding-left: 44px; /* ensure icon doesn't overlap text */
}

.trailingIcon {
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  pointer-events: none;
  z-index: 1;
}