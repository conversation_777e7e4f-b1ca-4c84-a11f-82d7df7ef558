import { PageContainer } from '../../../../components/UI/Page/PageContainer/PageContainer'
import { useRouter } from 'next/router'
import { useState, useContext } from 'react'
import { RelationshipsDashboard } from '../../../../components/UI/Dashboards/RelationshipsDashboard/RelationshipsDashboard'
import { TabMenu } from 'primereact/tabmenu'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { defaultSubtitle } from '../../../../components/FormBuilder/DndComponents/ComponentPanel/ComponentPanel'
import LexicalEditor from '../../../../components/LexicalEditor/LexicalEditor'
import BreadCrumbs from '../../../../components/UI/BreadCrumbs/BreadCrumbs'
import UserProfileContext from '../../../../public/UserProfileContext/UserProfileContext'
import Image from 'next/image'
import clsx from 'clsx'
import PrimaryButton from '../../../../components/UI/PrimaryButton/PrimaryButton'
import Button from '../../../../components/UI/Button/Button'
import TextInput from '../../../../components/UI/Input/TextInput/TextInput'
import TextareaInput from '../../../../components/UI/Input/TextareaInput/TextareaInput'
import SelectInput from '../../../../components/UI/Input/SelectInput/SelectInput'

import Backarrow from '../../../../svg/metronic/back_metronic.svg'
import SubmittedTicketIcon from '../../../../images/itsm_submitted_icon.png'
import InProgressTicketIcon from '../../../../images/itsm_in_progress_icon.png'
import CompletedTicketIcon from '../../../../images/itsm_completed_icon.png'
import CloseTicketIcon from '../../../../images/itsm_close_ticket_icon.png'
import EditTicketIcon from '../../../../images/itsm_edit_ticket_icon.png'
import ReminderTicketIcon from '../../../../images/itsm_reminder_ticket_icon.png'
import SoftwareRequest from '../../../../images/SoftwareRequests.png' // Remove this once we implement the API for returning a submitted ticket.

import styles from './index.module.css'

export default function TicketView() {
  const router = useRouter()
  const { id } = router.query
  const userProfile = useContext(UserProfileContext)
  const [isReminderEnabled, setIsReminderEnabled] = useState(false)
  const [activeTab, setActiveTab] = useState(0)

  // Mock ticket data - replace with actual API call
  const ticketData = {
    id: '#MI673',
    title: 'Canvas Account Login Issues',
    submitter: 'John Smith',
    department: 'Sales',
    email: '<EMAIL>',
    priority: 'P2',
    dateSubmitted: '07/19/2025 4:21 P.M',
    office: '202A',
    location: 'North Building',
    phoneNumber: '************',
    assignedTo: 'Berkley Esherwood',
    lastUpdated: '07/19/2025 4:21 P.M'
  }

  const tabItems = [
    { label: 'Communications', icon: 'pi pi-comments' },
    { label: 'Attachments', icon: 'pi pi-clipboard' },
    { label: 'Audit Log', icon: 'pi pi-eye' }
    // TODO: Add other options for technicians later.
  ]

  return (
    <PageContainer theme="metronic">
      <div>
        {/* Header with back arrow */}
        <div className={styles.headerContainer}>
          <Image src={Backarrow} alt="Back" onClick={() => router.back()} className={styles.backArrow} />
          <BreadCrumbs title={'My Tickets'} breadcrumbItems={[{ label: `${ticketData.id} ${ticketData.title}` }]} theme="metronic" />
        </div>

        {/* Ticket Header */}
        <div>
          <h1 className={styles.ticketTitle}>
            {ticketData.id} {ticketData.title}
          </h1>
        </div>

        <TicketDetails ticketData={ticketData} />

        <div className={styles.ticketWidgetsContainer}>
          <VisualWorkflow currentStatus="Submitted" />
          <TicketActions isReminderEnabled={isReminderEnabled} />
        </div>

        <IssueInformation ticketData={ticketData} />

        <div className={styles.relationshipsDashboardContainer}>
          <RelationshipsDashboard formSubmission={{ id: 'temp-ticket-' + (id || 'unknown') }} theme="metronic" />
        </div>

        <div className={styles.commentsContainer}>
          <div className={styles.tabMenuContainer}>
            <TabMenu model={tabItems} activeIndex={activeTab} onTabChange={(e) => setActiveTab(e.index)} />
            <Button label="Filter" icon={<i className="pi pi-filter"></i>} buttonstyle={{ backgroundColor: '#024F7C', color: 'white' }} />
          </div>
          <CommentsSection activeTab={activeTab} />
        </div>
      </div>
    </PageContainer>
  )
}

const TicketDetails = ({ ticketData }) => (
  <>
    {/* Ticket Details Grid */}
    <div className={styles.ticketDetailsContainer}>
      <div className={styles.detailsGrid}>
        {/* First Row */}
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Submitter:</span>
          <span className={styles.detailValue}>{ticketData.submitter}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Department:</span>
          <span className={styles.detailValue}>{ticketData.department}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Email:</span>
          <span className={styles.detailValue}>{ticketData.email}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Priority:</span>
          <span className={styles.detailValue}>{ticketData.priority}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Date Submitted:</span>
          <span className={styles.detailValue}>{ticketData.dateSubmitted}</span>
        </div>

        {/* Second Row */}
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Office:</span>
          <span className={styles.detailValue}>{ticketData.office}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Location:</span>
          <span className={styles.detailValue}>{ticketData.location}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Phone Number:</span>
          <span className={styles.detailValue}>{ticketData.phoneNumber}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Assigned To:</span>
          <span className={styles.detailValue}>{ticketData.assignedTo}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>Last Updated:</span>
          <span className={styles.detailValue}>{ticketData.lastUpdated}</span>
        </div>
      </div>
    </div>
    <div className={styles.titleUnderline}></div>
  </>
)

const VisualWorkflow = ({ currentStatus }) => {
  const workflowStages = [
    {
      id: 'submitted',
      name: 'Submitted',
      status: 'Done',
      icon: SubmittedTicketIcon,
      bgColor: '#007BFF',
      isActive: currentStatus === 'Submitted'
    },
    {
      id: 'in-progress',
      name: 'In Progress',
      status: 'Pending',
      icon: InProgressTicketIcon,
      bgColor: '#FFC107',
      isActive: currentStatus === 'In Progress'
    },
    {
      id: 'completed',
      name: 'Completed',
      status: 'Pending',
      icon: CompletedTicketIcon,
      bgColor: '#28A745',
      isActive: currentStatus === 'Completed'
    }
  ]

  return (
    <div className={styles.workflowContainer}>
      <div className={styles.workflowHeader}>
        <h3 className={styles.workflowTitle}>Visual Workflow</h3>
        <div className={styles.currentStatus}>
          Status: <span className={styles.statusValue}>{currentStatus}</span>
        </div>
      </div>

      <div className={styles.workflowStages}>
        {workflowStages.map((stage) => (
          <div key={stage.id} className={styles.stageContainer}>
            <div className={styles.stageContent}>
              <div className={styles.stageIcon} style={{ backgroundColor: stage.bgColor }}>
                <Image src={stage.icon} alt={stage.name} width={24} height={24} className={styles.iconImage} />
              </div>
              <div className={styles.stageLabels}>
                <div className={styles.stageName}>{stage.name}</div>
                <div className={styles.stageStatus}>{stage.status}</div>
              </div>
            </div>
          </div>
        ))}
        <div className={styles.progressBars}>
          <div className={clsx(styles.progressBar, styles.progressBarPrimary)}></div>
          <div className={clsx(styles.progressBar, styles.progressBarSecondary)}></div>
        </div>
      </div>
    </div>
  )
}

const TicketActions = ({ isReminderEnabled }) => {
  const handleCloseTicket = () => {
    console.log('Close ticket clicked')
  }

  const handleEditTicket = () => {
    console.log('Edit ticket clicked')
  }

  const handleSendReminder = () => {
    console.log('Send reminder clicked')
  }

  return (
    <div className={styles.ticketActionsContainer}>
      <h3 className={styles.actionsTitle}>Ticket Actions</h3>
      <div className={styles.actionsButtons}>
        <div className={styles.actionButtonWrapper}>
          <Image src={CloseTicketIcon} alt="Close Ticket" width={16} height={16} className={styles.actionIcon} />
          <PrimaryButton
            text="Close Ticket"
            onClick={handleCloseTicket}
            width={120}
            height={35}
            fontSize={14}
            buttonstyle={{ backgroundColor: '#024F7C' }}
            className={styles.actionButton}
          />
        </div>

        <div className={styles.actionButtonWrapper}>
          <Image src={EditTicketIcon} alt="Edit Ticket" width={16} height={16} className={styles.actionIcon} />
          <PrimaryButton
            text="Edit Ticket"
            onClick={handleEditTicket}
            width={120}
            height={35}
            fontSize={14}
            buttonstyle={{ backgroundColor: '#024F7C' }}
            className={styles.actionButton}
          />
        </div>

        <div className={styles.actionButtonWrapper}>
          <Image src={ReminderTicketIcon} alt="Send Reminder" width={16} height={16} className={styles.actionIcon} />
          <PrimaryButton
            text="Send Reminder"
            onClick={handleSendReminder}
            width={140}
            height={35}
            fontSize={14}
            disabled={!isReminderEnabled}
            buttonstyle={{
              backgroundColor: isReminderEnabled ? '#024F7C' : '#9F9F9F'
            }}
            className={styles.actionButton}
          />
        </div>
      </div>
    </div>
  )
}

const IssueInformation = ({ ticketData }) => {
  // Mock attachment data - replace with actual attachment data
  const attachments = [{ id: 1, name: 'screenshot1.png', type: 'image', url: SoftwareRequest }]

  return (
    <div className={styles.issueInformationContainer}>
      <h3 className={styles.issueTitle}>Issue Information</h3>

      {/* Form Fields Row */}
      <div className={styles.formFieldsRow}>
        <SelectInput
          label="Product/Category"
          value="Software"
          theme="metronic"
          customWidth={{
            display: 'flex',
            flexDirection: 'column',
            rowGap: '10px',
            width: '23%',
            color: 'var(--primary-bg-blackPearl)'
          }}
          evenLayout={true}
          options={[]}
          disabled={true}
        />
        <SelectInput
          label="Sub Category"
          value="Account Issues"
          theme="metronic"
          customWidth={{
            display: 'flex',
            flexDirection: 'column',
            rowGap: '10px',
            width: '23%',
            color: 'var(--primary-bg-blackPearl)'
          }}
          evenLayout={true}
          options={[]}
          disabled={true}
        />
        <TextInput label="Issue Occurred On" value="07/18/2025" theme="metronic" width="23%" disabled={true} />
        <TextInput label="Subject" value={ticketData.title} theme="metronic" width="23%" disabled={true} />
      </div>

      {/* Description Field */}
      <div className={styles.descriptionSection}>
        <TextareaInput
          label="Describe The Issue"
          value="I am having issues logging into canvas, can you please take a look into my account."
          theme="metronic"
          width="100%"
          height="120px"
          disabled={true}
          rows={4}
        />
      </div>

      {/* Attachments Section */}
      <div className={styles.attachmentsSection}>
        <h4 className={styles.attachmentsTitle}>Attachments:</h4>
        <div className={styles.attachmentsList}>
          {attachments.map((attachment) => (
            <div key={attachment.id} className={styles.attachmentItem}>
              {attachment.type === 'image' && attachment.url ? (
                <Image src={attachment.url} alt={attachment.name} width={80} height={60} className={styles.attachmentImage} />
              ) : (
                <div className={styles.attachmentFile}>
                  <span className={styles.fileIcon}>📄</span>
                  <span className={styles.fileName}>{attachment.name}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

const CommentsSection = ({ activeTab }) => {
  // Mock comments data - replace with actual API call when implemented
  const commentsData = [
    {
      id: 1,
      author: 'John Smith',
      content: 'Yes, here it is, let me know if you need anything else.\nThank you.',
      timestamp: '07/07/25 2:01 P.M PST',
      hasAttachments: true,
      attachments: [
        {
          id: 1,
          name: 'screenshot1.png',
          type: 'image',
          url: SoftwareRequest
        }
      ]
    },
    {
      id: 2,
      author: 'Berkley Esherwood',
      content: 'Hi @John Smith, can you please provide the latest login attempt screenshot.',
      timestamp: '07/21/25 1:46 P.M PST',
      hasAttachments: false,
      attachments: []
    }
  ]

  // Filter comments based on active tab
  const getFilteredComments = () => {
    switch (activeTab) {
      case 0: // Communications - show all comments
        return commentsData
      case 1: // Attachments - show only comments with attachments
        return commentsData.filter((comment) => comment.hasAttachments)
      case 2: // Audit Log - handled separately with AuditLog component
        return commentsData
      default:
        return commentsData
    }
  }

  const filteredComments = getFilteredComments()

  // Show Audit Log component for Audit Log tab
  if (activeTab === 2) {
    return <AuditLog />
  }

  return (
    <>
      <div className={styles.commentEditorContainer}>
        <LexicalEditor
          name="comment"
          value={defaultSubtitle}
          onChange={() => {}} // TODO: Add onChange function after implementing the API for returning a submitted ticket.
        />
      </div>

      <div className={styles.commentsSection}>
        {filteredComments.map((comment) => (
          <CommentItem key={comment.id} comment={comment} />
        ))}
      </div>
    </>
  )
}

const CommentItem = ({ comment }) => {
  return (
    <div className={styles.commentItem}>
      <div className={styles.commentHeader}>
        <div className={styles.commentAuthorSection}>
          <i
            className="pi pi-user"
            style={{
              fontSize: '2rem',
              color: '#6c757d',
              marginRight: '12px',
              border: '2px solid #dee2e6',
              borderRadius: '50%',
              padding: '8px',
              backgroundColor: '#f8f9fa'
            }}
          ></i>
          <div className={styles.commentAuthorInfo}>
            <span className={styles.commentAuthor}>{comment.author}</span>
          </div>
        </div>
        <span className={styles.commentTimestamp}>{comment.timestamp}</span>
      </div>

      <div className={styles.commentContent}>
        <p className={styles.commentText}>
          {comment.content.split('\n').map((line, index) => (
            <span key={index}>
              {line}
              {index < comment.content.split('\n').length - 1 && <br />}
            </span>
          ))}
        </p>

        {comment.hasAttachments && comment.attachments.length > 0 && (
          <div className={styles.commentAttachments}>
            <h4 className={styles.commentAttachmentsTitle}>Attachments:</h4>
            <div className={styles.commentAttachmentsList}>
              {comment.attachments.map((attachment) => (
                <div key={attachment.id} className={styles.commentAttachmentItem}>
                  {attachment.type === 'image' && attachment.url ? (
                    <Image src={attachment.url} alt={attachment.name} width={80} height={60} className={styles.commentAttachmentImage} />
                  ) : (
                    <div className={styles.commentAttachmentFile}>
                      <span className={styles.commentFileIcon}>📄</span>
                      <span className={styles.commentFileName}>{attachment.name}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

const AuditLog = () => {
  // Mock audit log data - replace with actual API call when implemented
  const auditLogData = [
    {
      action: 'Submitted',
      userGroup: 'John Smith',
      dateTime: '21-Jul-2025, 6:14:06 AM PDT',
      userComments: 'Submitted'
    }
  ]

  const auditLogHeader = () => (
    <div className={styles.auditLogHeader}>
      <span className="text-xl font-bold m-2">Audit Log</span>
    </div>
  )

  return (
    <div className={styles.auditLogContainer}>
      <DataTable value={auditLogData} className="custom-lead" header={auditLogHeader} style={{ marginTop: '1rem' }}>
        <Column field="action" header="Action" />
        <Column field="userGroup" header="User/Group" />
        <Column field="dateTime" header="Date and Time" />
        <Column field="userComments" header="User Comments" />
      </DataTable>
    </div>
  )
}
