/* TODO: Refactor this file so it follows the DRY principle better. */

.breadcrumbsContainer {
  margin-bottom: 20px;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.backArrow {
  cursor: pointer;
  width: 45px;
  height: 45px;
  transition: opacity 0.2s ease;
}

.backArrow:hover {
  opacity: 0.7;
}

.headerContainer {
  margin-bottom: 10px;
}

.categorySettingsHeader {
  color: #012853;
  font-size: 0.85rem;
  font-weight: 600;
  background-color: rgba(0, 157, 255, 0.07);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.categorySettingsHeader h2 {
  margin: 0;
  padding: 10px;
}

.categorySettingsHeaderUnderline {
  width: 100%;
  height: 3px;
  background-color: #012853;
}

.settingsInformationContent {
  margin-bottom: 10px;
}

.settingsInformationContent p {
  margin: 0;
} 

/* Category Information */

.categoryInformationContent {
  margin: 10px 0 !important;
}

.categoryInformationTable {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  margin: 10px 0;
}

.categoryInformationTableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #1b84ff0d;
  color: #515151;
  padding: 1rem;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
}

.categoryInformationTableHeader h3 {
  margin: 0;
}

/* Weight Value Assignment */

.weightValueAssignment {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.weightValueAssignmentContainer {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.weightValueAssignmentLabel {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin-left: 0.25rem;
}

.severityTestCalculatorHeader {
  font-size: 1.25rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
}

.severityTestCalculatorFormula {
  margin: 0 0 0.5rem 0;
}


/* Tier Ranges */

.rangeValueFieldsContainer {
  display: flex;
  gap: 10px;
}

.rangeValueFieldsItem {
  display: flex;
  flex-direction: column;
  gap: 5px;
  justify-content: flex-end;
}

.rangeValueFieldsLabel {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin-left: 0.25rem;
}

.tierSLAContainer {
  display: flex;
  gap: 10px;
  margin: 10px 0;
}

.tierSLAItem {
  display: flex;
  gap: 5px;
}

/* Modal Styles */

.modalContent {
  padding: 0;
}

.modalInstructions {
  margin-bottom: 20px;
  color: #012853;
  font-size: 0.95rem;
  line-height: 1.5;
}

.modalSection {
  margin-bottom: 20px;
}

.modalSectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
  margin: 0 0 15px 0;
}

.modalTableHeader {
  background-color: #1b84ff0d;
  padding: 10px 15px;
  border-radius: 8px 8px 0 0;
  border: 1px solid #5151511a;
  margin-bottom: 0;
}

.modalTableHeaderTitle {
  font-weight: 600;
  color: #515151;
  font-size: 0.95rem;
}

.modalUserValuesContainer {
  border: 1px solid #5151511a;
  border-top: none;
  border-radius: 0 0 8px 8px;
  background-color: white;
}

.modalUserValueRow {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  gap: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.modalUserValueRow:last-child {
  border-bottom: none;
}

.modalUserValueItem {
  flex: 1;
}

.modalUserValueAction {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deleteIcon {
  cursor: pointer;
  transition: opacity 0.2s ease;
  font-size: 1.25rem;
}

.deleteIcon:hover {
  opacity: 0.7;
}

.modalActions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-start;
}

.addNewUserValueButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  border: none;
  color: #1b84ff;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s ease;
}

.addNewUserValueButton:hover {
  color: #0056cc;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

/* Category Edit Form Styles */

.categoryEditContainer {
  margin-bottom: 10px;
}

.categoryEditFormRow {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.categoryEditFormGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.categoryEditMetadataRow {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.categoryEditMetadataGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: 150px;
}

.categoryEditMetadataLabel {
  font-size: 0.9rem;
  font-weight: 600;
  color: #012853;
  font-family: 'Open Sans';
}

.categoryEditMetadataValue {
  font-size: 0.9rem;
  color: #515151;
  font-family: 'Open Sans';
}

/* Sub-Categories Edit Styles */

.subCategoriesContainer {
  margin: 10px 0;
}

.subCategoriesHeader {
  display: flex;
  margin-bottom: 10px;
}

.subCategoriesHeaderItem {
  flex: 1;
}

.subCategoriesContent {
  margin-bottom: 20px;
}

.subCategoryRow {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.subCategoryRow:last-child {
  margin-bottom: 0;
}

.subCategoryItem {
  flex: 1;
  gap: 5px;
  display: flex;
  flex-direction: column;
}

.subCategoryAction {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
}

.subCategoriesActions {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
  display: flex;
  justify-content: flex-start;
}

.subCategoryItemSeverityScore {
  display: flex;
  align-items: center;
  gap: 10px;
}