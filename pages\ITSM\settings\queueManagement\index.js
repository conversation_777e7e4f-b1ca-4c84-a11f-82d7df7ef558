// Components
import { useRouter } from 'next/router'
import { PageContainer } from '../../../../components/UI/Page/PageContainer/PageContainer'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useState } from 'react'
import { Toggle } from '../../../../components/FormBuilder/Settings/UI/Toggle/Toggle'
import { ConditionalDisplay } from '../../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import Button from '../../../../components/UI/Button/Button'
import BreadCrumbs from '../../../../components/UI/BreadCrumbs/BreadCrumbs'
import Image from 'next/image'
import Modal from '../../../../components/UI/Modal/Modal'
import TextInput from '../../../../components/UI/Input/TextInput/TextInput'
import TextareaInput from '../../../../components/UI/Input/TextareaInput/TextareaInput'
import SelectInput from '../../../../components/UI/Input/SelectInput/SelectInput'
import DragButton from '../../../../components/FormBuilder/DndComponents/DragButton/DragButton'

// DnD Kit
import { DndContext, closestCenter, useSensor, useSensors, MouseSensor, TouchSensor } from '@dnd-kit/core'
import { arrayMove, SortableContext, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

// Images
import Backarrow from '../../../../svg/metronic/back_metronic.svg'
import CubeIcon from '../../../../images/itsm_request_new_device_icon.png'
import AddIcon from '../../../../svg/metronic/plus.svg'
import DeleteIcon from '../../../../svg/metronic/delete.svg'

// Styles
import styles from './index.module.css'

export default function QueueManagement() {
  const router = useRouter()
  const [isConfiguring, setIsConfiguring] = useState(false)

  return (
    <PageContainer theme="metronic">
      <div className={styles.breadcrumbsContainer}>
        <Image
          src={Backarrow}
          alt="Back"
          onClick={() => (isConfiguring ? setIsConfiguring(false) : router.back())}
          className={styles.backArrow}
        />
        <BreadCrumbs
          title={isConfiguring ? 'Queue Configuration' : 'Queue Management'}
          breadcrumbItems={[
            { label: `Settings` },
            { label: `Queue Management` },
            ...(isConfiguring ? [{ label: `Queue Configuration` }] : [])
          ]}
          theme="metronic"
        />
      </div>
      <ConditionalDisplay condition={!isConfiguring}>
        <WeightValueAssignment setIsConfiguring={setIsConfiguring} />
        <LocationsAssignment setIsConfiguring={setIsConfiguring} />
      </ConditionalDisplay>
      <ConditionalDisplay condition={isConfiguring}>
        <QueueConfigurationForm />
      </ConditionalDisplay>
    </PageContainer>
  )
}

const QueueConfigurationForm = () => {
  return (
    <>
      <LocationSetup />
      <TechnicianWhitelist />
      <SupervisorRoleSetup />
      <CategorySubCategorySettings />
      <TicketSettings />
    </>
  )
}

const LocationSetup = () => {
  const [selectedLocation, setSelectedLocation] = useState(null)

  const locationOptions = [
    { label: 'West Building', value: 'west' },
    { label: 'East Building', value: 'east' },
    { label: 'North Building', value: 'north' },
    { label: 'South Building', value: 'south' }
  ]

  return (
    <>
      <QueueManagementHeader header="Location Setup" />
      <div className={styles.configurationSection}>
        <p className={styles.configurationInstructions}>Please select the location where the support team is located.</p>

        <div className={styles.configurationFormGroup}>
          <label className={styles.configurationLabel}>Location</label>
          <SelectInput
            value={selectedLocation}
            options={locationOptions}
            onChange={(e) => setSelectedLocation(e.value)}
            placeholder="Select location"
            theme="metronic"
          />
        </div>

        <div className={styles.configurationMetadata}>
          <div className={styles.configurationMetadataRow}>
            <div className={styles.configurationMetadataGroup}>
              <label className={styles.configurationMetadataLabel}>Created By</label>
              <span className={styles.configurationMetadataValue}>$admin</span>
            </div>
            <div className={styles.configurationMetadataGroup}>
              <label className={styles.configurationMetadataLabel}>Created</label>
              <span className={styles.configurationMetadataValue}>SorathonDate</span>
            </div>
            <div className={styles.configurationMetadataGroup}>
              <label className={styles.configurationMetadataLabel}>Modified By</label>
              <span className={styles.configurationMetadataValue}>--</span>
            </div>
            <div className={styles.configurationMetadataGroup}>
              <label className={styles.configurationMetadataLabel}>Date Modified</label>
              <span className={styles.configurationMetadataValue}>--</span>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const TechnicianWhitelist = () => {
  const [selectedUserGroups, setSelectedUserGroups] = useState(null)

  const userGroupOptions = [
    { label: 'IT Support Team', value: 'it_support' },
    { label: 'Help Desk', value: 'help_desk' },
    { label: 'Technical Staff', value: 'technical_staff' }
  ]

  return (
    <>
      <QueueManagementHeader header="Technician Whitelist" />
      <div className={styles.configurationSection}>
        <p className={styles.configurationInstructions}>Please select which user groups have access to this queue.</p>

        <div className={styles.configurationFormGroup}>
          <label className={styles.configurationLabel}>User Groups</label>
          <SelectInput
            value={selectedUserGroups}
            options={userGroupOptions}
            onChange={(e) => setSelectedUserGroups(e.value)}
            placeholder="Select user groups"
            theme="metronic"
          />
        </div>
      </div>
    </>
  )
}

const SupervisorRoleSetup = () => {
  const [selectedRoles, setSelectedRoles] = useState(null)

  const roleOptions = [
    { label: 'IT Manager', value: 'it_manager' },
    { label: 'Senior Technician', value: 'senior_tech' },
    { label: 'Team Lead', value: 'team_lead' }
  ]

  return (
    <>
      <QueueManagementHeader header="Supervisor Role Setup" />
      <div className={styles.configurationSection}>
        <p className={styles.configurationInstructions}>Please select the roles that are supervisors based on your user roles.</p>

        <div className={styles.configurationFormGroup}>
          <label className={styles.configurationLabel}>Roles</label>
          <SelectInput
            value={selectedRoles}
            options={roleOptions}
            onChange={(e) => setSelectedRoles(e.value)}
            placeholder="Select roles"
            theme="metronic"
          />
        </div>
      </div>
    </>
  )
}

const CategorySubCategorySettings = () => {
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [selectedSubCategory, setSelectedSubCategory] = useState(null)

  const categoryOptions = [
    { label: 'Software', value: 'software' },
    { label: 'Hardware', value: 'hardware' },
    { label: 'Network', value: 'network' }
  ]

  const subCategoryOptions = [
    { label: 'Application Issues', value: 'app_issues' },
    { label: 'Installation Problems', value: 'install_problems' },
    { label: 'Performance Issues', value: 'performance' }
  ]

  return (
    <>
      <QueueManagementHeader header="Category/Sub-Category Settings" />
      <div className={styles.configurationSection}>
        <p className={styles.configurationInstructions}>Please select the category and sub-category for this queue.</p>
        <p className={styles.configurationNote}>
          Note: You cannot create multiple queues with the same location, category, and sub-category combination.
        </p>

        <div className={styles.configurationFormRow}>
          <div className={styles.configurationFormGroup}>
            <label className={styles.configurationLabel}>Category Selection</label>
            <SelectInput
              value={selectedCategory}
              options={categoryOptions}
              onChange={(e) => setSelectedCategory(e.value)}
              placeholder="Select category"
              theme="metronic"
            />
          </div>
          <div className={styles.configurationFormGroup}>
            <label className={styles.configurationLabel}>Sub-Categories Selection</label>
            <SelectInput
              value={selectedSubCategory}
              options={subCategoryOptions}
              onChange={(e) => setSelectedSubCategory(e.value)}
              placeholder="Select sub-category"
              theme="metronic"
            />
          </div>
        </div>
      </div>
    </>
  )
}

const SortableTechnicianItem = ({
  technician,
  priority,
  assigneeOptions,
  updateTechnicianInColumn,
  deleteTechnicianFromColumn,
  canDelete
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: technician.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  }

  return (
    <div ref={setNodeRef} style={style} className={styles.technicianItem} {...attributes}>
      <div className={styles.technicianItemHeader}>
        <span className={styles.orderNumber}>{technician.order}</span>
        <div className={styles.dragHandleContainer} {...listeners}>
          <i className="pi pi-bars" style={{ color: '#015187', fontSize: '1rem' }} />
        </div>
        {canDelete && (
          <div className={styles.technicianItemAction}>
            <Image
              src={DeleteIcon}
              alt="Delete"
              width={30}
              height={30}
              onClick={() => deleteTechnicianFromColumn(priority, technician.id)}
              className={styles.deleteIcon}
            />
          </div>
        )}
      </div>
      <div className={styles.technicianSelectContainer}>
        <SelectInput
          value={technician.assignee}
          options={assigneeOptions}
          onChange={(e) => updateTechnicianInColumn(priority, technician.id, e.value)}
          placeholder="Choose Assignee"
          theme="metronic"
        />
      </div>
    </div>
  )
}

const TechnicianColumn = ({
  priority,
  title,
  technicians,
  assigneeOptions,
  updateTechnicianInColumn,
  deleteTechnicianFromColumn,
  addTechnicianToColumn
}) => {
  return (
    <div className={styles.technicianColumn}>
      <div className={styles.technicianColumnHeader}>
        <h4 className={styles.technicianColumnTitle}>{title}</h4>
        <button className={styles.addTechnicianButton} onClick={() => addTechnicianToColumn(priority)}>
          <Image src={AddIcon} alt="Add" width={18} height={18} />
        </button>
      </div>
      <SortableContext items={technicians.map((item) => item.id)} strategy={verticalListSortingStrategy}>
        <div className={styles.technicianColumnContent}>
          {technicians.map((technician) => (
            <SortableTechnicianItem
              key={technician.id}
              technician={technician}
              priority={priority}
              assigneeOptions={assigneeOptions}
              updateTechnicianInColumn={updateTechnicianInColumn}
              deleteTechnicianFromColumn={deleteTechnicianFromColumn}
              canDelete={technicians.length > 1}
            />
          ))}
        </div>
      </SortableContext>
    </div>
  )
}

const TicketSettings = () => {
  const [assignmentMethod, setAssignmentMethod] = useState(false) // true = manual, false = automatic
  const [technicianColumns, setTechnicianColumns] = useState({
    p1: [{ id: 'p1-1', assignee: null, order: 1 }],
    p2: [{ id: 'p2-1', assignee: null, order: 1 }],
    p3: [{ id: 'p3-1', assignee: null, order: 1 }]
  })

  const assigneeOptions = [
    { label: 'Choose Assignee', value: null },
    { label: 'John Smith', value: 'john_smith' },
    { label: 'Jane Doe', value: 'jane_doe' },
    { label: 'Mike Johnson', value: 'mike_johnson' }
  ]

  const addTechnicianToColumn = (priority) => {
    setTechnicianColumns((prev) => {
      const currentColumn = prev[priority]
      const newId = `${priority}-${Date.now()}`
      const newOrder = currentColumn.length + 1

      return {
        ...prev,
        [priority]: [...currentColumn, { id: newId, assignee: null, order: newOrder }]
      }
    })
  }

  const deleteTechnicianFromColumn = (priority, id) => {
    setTechnicianColumns((prev) => {
      const updatedColumn = prev[priority].filter((item) => item.id !== id)
      // Reorder the remaining items
      const reorderedColumn = updatedColumn.map((item, index) => ({
        ...item,
        order: index + 1
      }))

      return {
        ...prev,
        [priority]: reorderedColumn.length > 0 ? reorderedColumn : [{ id: `${priority}-${Date.now()}`, assignee: null, order: 1 }]
      }
    })
  }

  const updateTechnicianInColumn = (priority, id, value) => {
    setTechnicianColumns((prev) => ({
      ...prev,
      [priority]: prev[priority].map((item) => (item.id === id ? { ...item, assignee: value } : item))
    }))
  }

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8
      }
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5
      }
    })
  )

  const handleDragEnd = (event) => {
    const { active, over } = event

    if (!over || active.id === over.id) return

    // Extract priority from IDs (format: 'p1-123', 'p2-456', etc.)
    const getColumnFromId = (id) => id.split('-')[0]
    const activeColumn = getColumnFromId(active.id)
    const overColumn = getColumnFromId(over.id)

    setTechnicianColumns((prev) => {
      // Moving within the same column
      if (activeColumn === overColumn) {
        const column = prev[activeColumn]
        const oldIndex = column.findIndex((item) => item.id === active.id)
        const newIndex = column.findIndex((item) => item.id === over.id)

        const newColumn = arrayMove(column, oldIndex, newIndex)

        // Update order numbers
        const reorderedColumn = newColumn.map((item, index) => ({
          ...item,
          order: index + 1
        }))

        return {
          ...prev,
          [activeColumn]: reorderedColumn
        }
      }

      // Moving between different columns
      else {
        const sourceColumn = prev[activeColumn]
        const destColumn = prev[overColumn]

        const activeItem = sourceColumn.find((item) => item.id === active.id)
        const overIndex = destColumn.findIndex((item) => item.id === over.id)

        // Remove from source column
        const newSourceColumn = sourceColumn.filter((item) => item.id !== active.id).map((item, index) => ({ ...item, order: index + 1 }))

        // Add to destination column with new ID
        const newActiveItem = {
          ...activeItem,
          id: `${overColumn}-${Date.now()}`,
          order: overIndex + 1
        }

        const newDestColumn = [...destColumn]
        newDestColumn.splice(overIndex, 0, newActiveItem)

        // Reorder destination column
        const reorderedDestColumn = newDestColumn.map((item, index) => ({
          ...item,
          order: index + 1
        }))

        return {
          ...prev,
          [activeColumn]:
            newSourceColumn.length > 0 ? newSourceColumn : [{ id: `${activeColumn}-${Date.now()}`, assignee: null, order: 1 }],
          [overColumn]: reorderedDestColumn
        }
      }
    })
  }

  return (
    <>
      <QueueManagementHeader header="Ticket Settings" />
      <div className={styles.configurationSection}>
        <p className={styles.configurationInstructions}>
          Select how tickets are managed. If a supervisor has to manually assign tickets or will tickets be assigned automatically.
        </p>

        <div className={styles.toggleItem}>
          <Toggle
            name="assignment"
            onChange={(e) => setAssignmentMethod(e.value)}
            checked={assignmentMethod}
            label="Manually Assign Tickets"
          />
        </div>

        {!assignmentMethod && (
          <div className={styles.configurationFormGroup}>
            <h4 className={styles.configurationSectionTitle}>Technician Assignment Ratio</h4>
            <div className={styles.technicianColumnsContainer}>
              <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                <div className={styles.technicianColumnsGrid}>
                  <TechnicianColumn
                    priority="p1"
                    title="P1 Technicians"
                    technicians={technicianColumns.p1}
                    assigneeOptions={assigneeOptions}
                    updateTechnicianInColumn={updateTechnicianInColumn}
                    deleteTechnicianFromColumn={deleteTechnicianFromColumn}
                    addTechnicianToColumn={addTechnicianToColumn}
                  />
                  <TechnicianColumn
                    priority="p2"
                    title="P2 Technicians"
                    technicians={technicianColumns.p2}
                    assigneeOptions={assigneeOptions}
                    updateTechnicianInColumn={updateTechnicianInColumn}
                    deleteTechnicianFromColumn={deleteTechnicianFromColumn}
                    addTechnicianToColumn={addTechnicianToColumn}
                  />
                  <TechnicianColumn
                    priority="p3"
                    title="P3 Technicians"
                    technicians={technicianColumns.p3}
                    assigneeOptions={assigneeOptions}
                    updateTechnicianInColumn={updateTechnicianInColumn}
                    deleteTechnicianFromColumn={deleteTechnicianFromColumn}
                    addTechnicianToColumn={addTechnicianToColumn}
                  />
                </div>
              </DndContext>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

const QueueManagementHeader = ({ header }) => {
  return (
    <div className={styles.headerContainer}>
      <div className={styles.queueManagementHeader}>
        <h2>{header}</h2>
      </div>
      <div className={styles.queueManagementHeaderUnderline}></div>
    </div>
  )
}

const QueueManagementModal = ({
  visible,
  onHide,
  header,
  textInputTitle,
  textInputPlaceholder,
  textAreaTitle,
  textAreaPlaceholder,
  setIsConfiguring,
  buttonLabel
}) => {
  const [queueName, setQueueName] = useState('')
  const [queueDescription, setQueueDescription] = useState('')

  return (
    <Modal header={header} visible={visible} onHide={onHide} width={50} backgroundColor="white" theme="metronic">
      <div className={styles.modalContent}>
        <p className={styles.modalInstructions}>
          To start the process of creating a new queue, please give your queue a name and description.
        </p>

        <div className={styles.modalSection}>
          <h4 className={styles.modalSectionTitle}>{textInputTitle}</h4>
          <TextInput
            required={true}
            placeholder={textInputPlaceholder}
            theme="metronic"
            value={queueName}
            onChange={(e) => setQueueName(e.target.value)}
          />
        </div>

        <div className={styles.modalSection}>
          <h4 className={styles.modalSectionTitle}>{textAreaTitle}</h4>
          <TextareaInput
            required={true}
            placeholder={textAreaPlaceholder}
            theme="metronic"
            value={queueDescription}
            onChange={(e) => setQueueDescription(e.target.value)}
          />
        </div>

        <div className={styles.modalSection}>
          <div className={styles.modalSectionButtons}>
            <Button
              label={buttonLabel}
              theme="metronic"
              width="180px"
              onClick={() => {
                onHide()
                setIsConfiguring(true)
              }}
            />
          </div>
        </div>
      </div>
    </Modal>
  )
}

const WeightValueAssignment = ({ setIsConfiguring }) => {
  const [visible, setVisible] = useState(false)

  const weightValueAssignmentData = [
    {
      queueTitle: 'IT Help Desk Software Issues - West',
      queueDescription: 'Tickets regarding software issues',
      location: 'West Building',
      pendingTickets: 10,
      groupAssignment: 'IT Help Desk',
      isActive: true,
      createdBy: 'John Doe',
      lastUpdatedBy: 'John Doe',
      lastUpdatedAt: '2021-01-01'
    },
    {
      queueTitle: 'IT Help Desk Software Issues - East',
      queueDescription: 'Tickets regarding software issues',
      location: 'East Building',
      pendingTickets: 10,
      groupAssignment: 'IT Help Desk',
      isActive: true,
      createdBy: 'John Doe',
      lastUpdatedBy: 'John Doe',
      lastUpdatedAt: '2021-01-01'
    }
  ]

  const weightValueAssignmentTableHeader = (
    <div className={styles.queueManagementTableHeader}>
      <h3>Weight Value Assignment</h3>
      <Button
        label="Add New Queue"
        theme="metronic"
        width="235px"
        icon={<Image src={CubeIcon} alt="Add" />}
        onClick={() => setVisible(true)}
      />
    </div>
  )

  return (
    <div className={styles.headerContainer}>
      <QueueManagementHeader header="Weight Value Assignment" />
      <DataTable value={weightValueAssignmentData} header={weightValueAssignmentTableHeader} className="custom-lead">
        <Column field="queueTitle" header="Queue Title" />
        <Column field="queueDescription" header="Description" />
        <Column field="location" header="Location" />
        <Column field="pendingTickets" header="Pending Tickets" />
        <Column field="groupAssignment" header="Group Assigned" />
        <Column field="isActive" header="Active" />
        <Column field="createdBy" header="Created By" />
        <Column field="lastUpdatedBy" header="Last Updated By" />
        <Column field="lastUpdatedAt" header="Last Updated At" />
      </DataTable>
      <QueueManagementModal
        visible={visible}
        onHide={() => setVisible(false)}
        header="Create New Queue"
        textInputTitle="Queue Name"
        textInputPlaceholder="Enter queue name"
        textAreaTitle="Queue Description"
        textAreaPlaceholder="Enter queue description"
        setIsConfiguring={setIsConfiguring}
        buttonLabel="Create Queue"
      />
    </div>
  )
}

const LocationsAssignment = ({ setIsConfiguring }) => {
  const [visible, setVisible] = useState(false)

  const locationsAssignmentData = [
    {
      location: 'West Building',
      locationDescription: 'West Building',
      groupsAssigned: '3',
      isActive: true,
      createdBy: 'John Doe',
      lastUpdatedBy: 'John Doe',
      lastUpdatedAt: '2021-01-01'
    }
  ]

  const locationsAssignmentTableHeader = (
    <div className={styles.queueManagementTableHeader}>
      <h3>Locations Assignment</h3>
      <Button
        label="Add New Location"
        theme="metronic"
        width="235px"
        icon={<Image src={CubeIcon} alt="Add" />}
        onClick={() => setVisible(true)}
      />
    </div>
  )

  return (
    <div className={styles.headerContainer}>
      <QueueManagementHeader header="Locations Assignment" />
      <DataTable value={locationsAssignmentData} header={locationsAssignmentTableHeader} className="custom-lead">
        <Column field="location" header="Location" />
        <Column field="locationDescription" header="Description" />
        <Column field="groupsAssigned" header="Groups Assigned" />
        <Column field="isActive" header="Active" />
        <Column field="createdBy" header="Created By" />
        <Column field="lastUpdatedBy" header="Last Updated By" />
        <Column field="lastUpdatedAt" header="Last Updated At" />
      </DataTable>
      <QueueManagementModal
        visible={visible}
        onHide={() => setVisible(false)}
        header="Create New Location"
        textInputTitle="Location Name"
        textInputPlaceholder="Enter location name"
        textAreaTitle="Location Description"
        textAreaPlaceholder="Enter location description"
        setIsConfiguring={setIsConfiguring}
        buttonLabel="Create Location"
      />
    </div>
  )
}
