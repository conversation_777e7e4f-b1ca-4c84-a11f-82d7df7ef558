import { Draggable } from "../Draggable/Draggable";
import { useState } from "react";
import Image from "next/image";
import TextFieldsSvg from "../../../../svg/Form builder/Text_Field.svg";
import ChoiceFieldsSvg from "../../../../svg/Form builder/Choice_Field.svg";
import H2Image from "../../../../images/Form Builder Icons/templates.png";
import clsx from "clsx";
import { ArrowIcon } from "../../../UI/SideNavbar/SubMenu/SubMenu";
import { BuildersMenu } from "../../../UI/SideNavbar/BuildersMenu/BuildersMenu";

import styles from "./ComponentPanel.module.css";

export default function ComponentPanel() {
  const [ismobileorTab, setIsmobileorTab] = useState(window.innerWidth < 992);
  const [isCollapsed, setIsCollapsed] = useState(false);

  return (
    <BuildersMenu
      isCollapsed={ismobileorTab ? false : isCollapsed}
      toggleCollapse={() => setIsCollapsed((prev) => !prev)}
    >
      <Container>
        <div className={styles.alignComponentParts}>
          <Image
            className={styles.marginRight}
            alt="image"
            src={H2Image}
            width={20}
            height={20}
          />
          <h2 className={styles.toolboxHeader}>Building Blocks</h2>
        </div>
        <AccordionItem componentName="header" label="Header" />
        <AccordionItem componentName="image" label="Image" />
        <Accordion label={"Text Components"}>
          <AccordionItem componentName="text" label="Short Text" />
          <AccordionItem componentName="textarea" label="Large Text" />
          <AccordionItem componentName="richText" label="Rich Text" />
        </Accordion>
        <AccordionItem componentName="calendar" label="Date Picker" />
        <AccordionItem componentName="time" label="Time" />
        <AccordionItem componentName="number" label="Number" />
        <AccordionItem componentName="file" label="File Upload" />
        <Accordion label={"Choice Components"}>
          <AccordionItem componentName="dropdown" label="Dropdown" />
          <AccordionItem componentName="multiselect" label="Multiselect" />
          <AccordionItem componentName="radiobutton" label="Single Choice" />
          <AccordionItem componentName="checkbox" label="Multiple Choice" />
        </Accordion>
        <AccordionItem componentName="mask" label="Mask" />
        <AccordionItem componentName="subtitle" label="Paragraph" />
        <AccordionItem componentName="signature" label="Signature" />
        <AccordionItem componentName="pageBreak" label="Page Break" />
        <AccordionItem
          componentName="calculatedField"
          label="Calculated Field"
        />
        <AccordionItem
          componentName="termsAndConditions"
          label="Terms & Conditions"
        />
        <AccordionItem
          componentName="advancedFileUpload"
          label="Advanced File Upload"
        />
        <AccordionItem
          componentName="versionedFileUpload"
          label="Versioned File Upload"
        />
        <AccordionItem componentName="scale" label="Scale Rating" />
        <AccordionItem componentName="stars" label="Star Rating" />
        <AccordionItem componentName="autoComplete" label="Autocomplete" />
        <AccordionItem componentName="jsTable" label="Spreadsheet" />
        <AccordionItem componentName="payments" label="Payments" />
        <AccordionItem componentName="heading" label="Heading" />
        <AccordionItem componentName="employeeLookup" label="Employee Lookup" />
        <AccordionItem componentName="accordion" label="Accordion" />
        <AccordionItem componentName="userSuggestion" label="User Suggestion" />
        <AccordionItem componentName="objectLink" label="Object Link" />
        <AccordionItem componentName="grid" label="Grid" />
        <AccordionItem componentName="objectLinkGrid" label="Object Link Grid" />
        <div className={styles.horizontalLine}></div>
        <div className={styles.alignComponentParts}>
          <Image
            className={styles.marginRight}
            alt="image"
            src={H2Image}
            width={20}
            height={20}
          />
          <h2 className={styles.toolboxHeader}>Templates</h2>
        </div>
        <AccordionItem componentName="address" label="Address" />
        <AccordionItem componentName="vendorDetails" label="Vendor Details" />
        <AccordionItem componentName="requestor" label="Requestor Field" />
      </Container>
    </BuildersMenu>
  );
}

export const Container = ({ children }) => {
  return <aside className={styles.container}>{children}</aside>;
};

const CreateOption = (component, index) => {
  return (
    <Draggable {...componentProperties[component]}>
      <label className={styles.labelStyles}>
        {component === "address" ? "Address" : componentNames[index]}
      </label>
    </Draggable>
  );
};

const CreateAccordion = (component, index, isVisible, setIsVisible) => {
  const textfields = ["text", "textarea", "richText"];
  const choicefields = ["dropdown", "multiselect", "radiobutton", "checkbox"];

  let components = [];
  let fieldIndex = index;

  if (component === "text") {
    for (const textfield of textfields) {
      components.push(CreateOption(textfield, fieldIndex));
      fieldIndex += 1;
    }
  } else if (component === "dropdown") {
    for (const choicefield of choicefields) {
      components.push(CreateOption(choicefield, fieldIndex));
      fieldIndex += 1;
    }
  }

  return (
    <div
      style={{
        padding:
          component === "text"
            ? "0.7rem 0 0.7rem 1.9rem"
            : "0.975rem 0 0.975rem 1.9rem",
      }}
    >
      <div className={styles.alignComponentParts}>
        <Image
          className={styles.marginRight}
          src={component === "text" ? TextFieldsSvg : ChoiceFieldsSvg}
          width={16}
          height={16}
          alt="Image"
        />
        <label className={styles.arrowContainer}>
          {component === "text" ? "Text Field" : "Choice Field"}
        </label>
        <span
          className={styles.arrow}
          style={{ transform: isVisible ? "rotate(180deg)" : "" }}
          onClick={() => setIsVisible((prev) => !prev)}
        >
          <i className="pi pi-angle-down" style={{ fontSize: "1.2rem" }} />
        </span>
      </div>
      {isVisible && components}
    </div>
  );
};

const Accordion = ({ iconPath, children, label, onClick }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = () => {
    setIsOpen((prev) => !prev);
    if (onClick) {
      onClick();
    }
  };

  return (
    <AccordionContainer>
      <AccordionHeaderContainer isOpen={isOpen} onClick={handleClick}>
        <Icon iconPath={iconPath ?? H2Image} />
        <AccordionLabel label={label} />
        <ArrowIcon isOpen={isOpen} />
      </AccordionHeaderContainer>
      <AccordionContentContainer isOpen={isOpen}>
        {children}
      </AccordionContentContainer>
    </AccordionContainer>
  );
};

const AccordionContainer = ({ children }) => {
  return <div className={styles.accordionContainer}>{children}</div>;
};

const AccordionItem = ({ componentName, label }) => {
  return (
    <Draggable {...componentProperties[componentName]}>
      <label className={styles.labelStyles}>{label}</label>
    </Draggable>
  );
};

const AccordionLabel = ({ label }) => {
  return <label className={styles.accordionLabel}>{label}</label>;
};

const Icon = ({ iconPath }) => {
  return (
    <div className={styles.iconContainer}>
      <Image width={16} height={16} src={iconPath} alt="accordionImage" />
    </div>
  );
};

const AccordionHeaderContainer = ({ children, onClick, isOpen }) => {
  return (
    <div
      className={`${styles.accordionHeaderContainer} ${
        isOpen && styles.accordionHeaderIsOpen
      }`}
      onClick={onClick}
      aria-expanded={isOpen}
      role="button"
      tabIndex={0}
    >
      {children}
    </div>
  );
};

const AccordionContentContainer = ({ children, isOpen }) => {
  // This function calculates the max height of the AccordionContentContainer
  // based on the number of AccordionItem components it contains
  // In future if we need nested Accordions:
  // we might need to check the Sub Accordion components and their
  // children based on if they are open or not
  const calculateMaxHeight = () => {
    const itemHeight = 53.5; // height for each AccordionItem

    if (!children) {
      return "0px";
    }

    if (!Array.isArray(children)) {
      children = [children];
    }

    const itemCount = children.filter(
      (child) => child.type && child.type.name === "AccordionItem"
    ).length;
    const maxHeight = itemHeight * itemCount;

    return `${maxHeight}px`;
  };

  return (
    <div
      className={clsx(
        styles.accordionContentContainer,
        isOpen && styles.accordionContainerIsOpen
      )}
      style={{ maxHeight: isOpen ? calculateMaxHeight() : "0" }}
    >
      {children}
    </div>
  );
};

export const CreateComponentList = () => {
  const [textfieldsVisible, setTextfieldsVisible] = useState(false);
  const [choicefieldsVisible, setChoicefieldsVisible] = useState(false);

  return componentTypes.map((component, index) => {
    if (component === "text") {
      return CreateAccordion(
        component,
        index,
        textfieldsVisible,
        setTextfieldsVisible
      );
    } else if (component === "textarea" || component === "richText") {
      return;
    }

    if (component === "dropdown") {
      return CreateAccordion(
        component,
        index,
        choicefieldsVisible,
        setChoicefieldsVisible
      );
    } else if (
      component === "multiselect" ||
      component === "radiobutton" ||
      component === "checkbox"
    ) {
      return;
    }

    return CreateOption(component, index);
  });
};

export const fullSizeClassName = "col-11 mlr-05";
export const halfSizeClassName = "col-5 mlr-05";

export const defaultSubtitle = JSON.stringify({
  root: {
    children: [
      {
        children: [],
        direction: null,
        format: "",
        indent: 0,
        type: "paragraph",
        version: 1,
      },
    ],
    direction: "ltr",
    format: "",
    indent: 0,
    type: "root",
    version: 1,
  },
});

export const defaultSubtitleForParagraph = JSON.stringify({
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: "normal",
            style: "",
            text: "Click Subtitle under Advanced to edit...",
            type: "text",
            version: 1,
          },
        ],
        direction: "ltr",
        format: "",
        indent: 0,
        type: "paragraph",
        version: 1,
      },
    ],
    direction: "ltr",
    format: "",
    indent: 0,
    type: "root",
    version: 1,
  },
});

export const defaultHeaderForHeading = JSON.stringify({
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 1,
            mode: "normal",
            style: "font-size: 25px;font-family: Arial;",
            text: "Heading",
            type: "text",
            version: 1,
          },
        ],
        direction: "ltr",
        format: "",
        indent: 0,
        type: "heading",
        version: 1,
        tag: "h1",
      },
    ],
    direction: "ltr",
    format: "",
    indent: 0,
    type: "root",
    version: 1,
  },
});

export const componentTypes = [
  "header",
  "image",
  "text",
  "textarea",
  "richText",
  "calendar",
  "time",
  "number",
  "file",
  "dropdown",
  "multiselect",
  "radiobutton",
  "checkbox",
  "mask",
  "subtitle",
  "signature",
  "pageBreak",
  "calculatedField",
  "termsAndConditions",
  "advancedFileUpload",
  "scale",
  "stars",
  "autoComplete",
  "tableComponent",
  "jsTable",
  "payments",
  "heading",
  "employeeLookup",
  "accordion",
  "userSuggestion",
  "objectLinkGrid",
  "grid",
];

export const componentNames = [
  "Header",
  "Image",
  "Short Text",
  "Large Text",
  "Rich Text",
  "Date Picker",
  "Time",
  "Number",
  "File Upload",
  "Dropdown",
  "Multiselect",
  "Single Choice",
  "Multiple Choice",
  "Mask",
  "Paragraph",
  "Signature",
  "Page Break",
  "Calculated Field",
  "Terms & Conditions",
  "Advanced File Upload",
  "Scale Rating",
  "Star Rating",
  "Autocomplete",
  "TableComponent",
  "Js Table",
  "Payments",
  "Heading",
  "Vendor Details",
  "Requestor Field",
  "employeeLookup",
  "Accordion",
  "userSuggestion",
  "objectLinkGrid",
  "grid",
];

export const fileTypeMetadata = {
  pdf: { label: ".pdf", value: "application/pdf" },
  png: { label: ".png", value: "image/png" },
  jpeg: { label: ".jpeg", value: "image/jpeg" },
  jpg: { label: ".jpg", value: "image/jpg" },
  docx: {
    label: ".docx",
    value:
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  },
  pptx: {
    label: ".pptx",
    value:
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  },
  xlsx: {
    label: ".xlsx",
    value: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  },
};

export const fileTypeOptions = [
  {
    label: fileTypeMetadata.pdf.label,
    value: {
      label: fileTypeMetadata.pdf.label,
      fileType: fileTypeMetadata.pdf.value,
    },
  },
  {
    label: fileTypeMetadata.png.label,
    value: {
      label: fileTypeMetadata.png.label,
      fileType: fileTypeMetadata.png.value,
    },
  },
  {
    label: fileTypeMetadata.jpeg.label,
    value: {
      label: fileTypeMetadata.jpeg.label,
      fileType: fileTypeMetadata.jpeg.value,
    },
  },
  {
    label: fileTypeMetadata.jpg.label,
    value: {
      label: fileTypeMetadata.jpg.label,
      fileType: fileTypeMetadata.jpg.value,
    },
  },
  {
    label: fileTypeMetadata.docx.label,
    value: {
      label: fileTypeMetadata.docx.label,
      fileType: fileTypeMetadata.docx.value,
    },
  },
  {
    label: fileTypeMetadata.pptx.label,
    value: {
      label: fileTypeMetadata.pptx.label,
      fileType: fileTypeMetadata.pptx.value,
    },
  },
  {
    label: fileTypeMetadata.xlsx.label,
    value: {
      label: fileTypeMetadata.xlsx.label,
      fileType: fileTypeMetadata.xlsx.value,
    },
  },
];

const defaultOption = { label: "Option 1", value: "Option 1" };

const defaultOptionsArray = [
  { label: defaultOption.label, value: defaultOption.value },
];

const alignmentOptions = {
  row: "row",
  column: "column",
  center: "center",
  rowReverse: "row-reverse",
};

const defaultGuidString = "00000000-0000-0000-0000-000000000000";

export const ObjectLinkingProperties = {
  moduleOptions: ["ezForms", "ezDocs", "ezSign"],
  selectedModule: "ezForms",
  sourceFormName: "",
  sourceFormData: {
    id: "",
    data: [],
  },
  primarySearchField: "",
  resultFields: [],
};

export const componentProperties = {
  header: {
    id: "0",
    name: "header",
    type: "header",
    guid: defaultGuidString,
    workflowId: "",
    width: "100%",
    height: "100%",
    aspectRatio: "1:1",
    label: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
  },
  image: {
    id: "1",
    name: "image",
    type: "image",
    guid: defaultGuidString,
    workflowId: "",
    width: "100%",
    height: "100%",
    aspectRatio: "1:1",
    label: "Image",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
  },
  text: {
    id: "2",
    name: "text",
    type: "text",
    guid: defaultGuidString,
    workflowId: "",
    label: "Short Text",
    subtitle: defaultSubtitle,
    defaultValue: "",
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    isForConcatenation: false,
    secured: false,
    validations: {
      minStringLength: {
        minLength: 0,
        message: "This field must have at least {minLength} characters",
      },
      maxStringLength: {
        maxLength: 63,
        message: "This field must have at most {maxLength} characters",
      },
    },
  },
  textarea: {
    id: "3",
    name: "textarea",
    type: "textarea",
    guid: defaultGuidString,
    workflowId: "",
    label: "Large Text",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
    validations: {
      minStringLength: {
        minLength: 0,
        message: "This field must have at least {minLength} characters",
      },
      maxStringLength: {
        maxLength: 255,
        message: "This field must have at most {maxLength} characters",
      },
    },
  },
  richText: {
    id: "4",
    name: "richText",
    type: "richText",
    guid: defaultGuidString,
    workflowId: "",
    label: "Rich Text",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    alignment: alignmentOptions.column,
    pageNumber: -1,
  },
  calendar: {
    id: "5",
    name: "calendar",
    type: "calendar",
    guid: defaultGuidString,
    workflowId: "",
    label: "Calendar",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    secured: false,
    isDefaultValueDynamic: false,
    isPastDateRestricted: false,
    isFutureDateRestricted: false,
    validations: {
      dateIsLesserOrEqual: {
        maxDate: new Date(2030, 0, 1),
        message: "This field must be less than or equal to the date {maxDate}",
      },
      dateIsGreaterOrEqual: {
        minDate: new Date(1900, 0, 1),
        message:
          "This field must be greater than or equal to the date {minDate}",
      },
    },
  },
  time: {
    id: "6",
    name: "time",
    type: "time",
    guid: defaultGuidString,
    workflowId: "",
    label: "Time",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    validations: {
      timeIsEqualOrGreater: {
        minTime: new Date(1900, 0, 1, 0, 0, 0),
        message:
          "This field must be greater than or equal to the time {minTime}",
      },
      timeIsEqualOrEarlier: {
        maxTime: new Date(3000, 0, 1, 23, 59, 59),
        message: "This field must be less than or equal to the time {maxTime}",
      },
    },
  },
  number: {
    id: "7",
    name: "number",
    type: "number",
    defaultValue: null,
    guid: defaultGuidString,
    workflowId: "",
    label: "Number",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    secured: false,
    isForConcatenation: false,
    numberFormatOptions: [
      { label: "Integer", value: "integer" },
      { label: "Decimal", value: "decimal" },
      { label: "Both", value: "both" },
    ],
    numberFormat: "integer",
    validations: {
      minNumValue: {
        minNum: 0,
        message:
          "This field must have a value greater than or equal to {minNum}",
      },
      maxNumValue: {
        maxNum: 2000000,
        message: "This field must have a value less than or equal to {maxNum}",
      },
    },
  },
  file: {
    id: "8",
    name: "file",
    type: "file",
    guid: defaultGuidString,
    workflowId: "",
    label: "File",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    fileTypeOptions: fileTypeOptions,
    multiple: false,
    validations: {
      minFileSize: {
        minSize: 0,
        message:
          "This field must have a file size greater than or equal to {minSize} MB",
      },
      maxFileSize: {
        maxSize: 5,
        message:
          "This field must have a file size less than or equal to {maxSize} MB",
      },
      allowedFileTypes: {
        types: fileTypeOptions.map((fileType) => fileType.value),
        message: "Allowed file types are: {types}",
      },
    },
  },
  dropdown: {
    id: "9",
    name: "dropdown",
    type: "dropdown",
    guid: defaultGuidString,
    workflowId: "",
    label: "Dropdown",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    isForConcatenation: false,
    secured: false,
    options: defaultOptionsArray,
    isSortingEnabled: false,
    sortingOrder: "ascending",
    sortingOptions: [
      { label: "ascending", value: "ascending" },
      { label: "descending", value: "descending" },
    ],
    isStickyEnabled: false,
    stickyOption: null,
  },
  multiselect: {
    id: "10",
    name: "multiselect",
    type: "multiselect",
    guid: defaultGuidString,
    workflowId: "",
    label: "Multiselect",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    isForConcatenation: false,
    secured: false,
    options: defaultOptionsArray,
    isSortingEnabled: false,
    sortingOrder: "ascending",
    sortingOptions: [
      { label: "ascending", value: "ascending" },
      { label: "descending", value: "descending" },
    ],
    isStickyEnabled: false,
    stickyOption: null,
  },
  radiobutton: {
    id: "11",
    name: "radiobutton",
    type: "radiobutton",
    guid: defaultGuidString,
    workflowId: "",
    label: "Single Choice",
    defaultValue: "",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    isMultiColumn: false,
    secured: false,
    pageNumber: -1,
    options: defaultOptionsArray,
    otherOptionEnabled: false,
  },
  checkbox: {
    id: "12",
    name: "checkbox",
    type: "checkbox",
    guid: defaultGuidString,
    workflowId: "",
    label: "Multiple Choice",
    defaultValue: [],
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    isMultiColumn: false,
    pageNumber: -1,
    options: defaultOptionsArray,
    otherOptionEnabled: false,
  },
  mask: {
    id: "13",
    name: "mask",
    type: "mask",
    guid: defaultGuidString,
    workflowId: "",
    label: "Mask",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
    secured: false,
    mask: "(999) 999-9999",
  },
  subtitle: {
    id: "14",
    name: "subtitle",
    type: "subtitle",
    guid: defaultGuidString,
    workflowId: "",
    label: "Paragraph",
    subtitle: defaultSubtitleForParagraph,
    alignment: alignmentOptions.column,
    pageNumber: -1,
    divClassName: fullSizeClassName,
  },
  signature: {
    id: "15",
    name: "signature",
    type: "signature",
    guid: defaultGuidString,
    workflowId: "",
    label: "Signature",
    defaultValue: "",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    fontOptions: [
      { label: "Times New Roman", value: "Times New Roman" },
      { label: "Arial", value: "Arial" },
      { label: "Georgia", value: "Georgia" },
      { label: "Cursive", value: "Cursive" },
      { label: "Calibri", value: "Calibri" },
      { label: "Tangerine", value: "Tangerine" },
    ],
    selectedFontStyle: "",
    validations: { signatureName: true },
  },
  pageBreak: {
    id: "16",
    name: "pageBreak",
    type: "pageBreak",
    guid: defaultGuidString,
    workflowId: "",
    backButtonText: "Back",
    nextButtonText: "Next",
    divClassName: fullSizeClassName,
  },
  calculatedField: {
    id: "17",
    name: "calculatedField",
    type: "calculatedField",
    guid: defaultGuidString,
    workflowId: "",
    label: "Calculated Field",
    defaultValue: "",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    calculation: "",
    pageNumber: -1,
  },
  termsAndConditions: {
    id: "18",
    name: "termsAndConditions",
    type: "termsAndConditions",
    guid: defaultGuidString,
    workflowId: "",
    label: "Terms and Conditions",
    headerText: "Terms and Conditions",
    forceUserRead: false,
    labelText: "Click {here} to read our Terms and Conditions",
    divClassName: fullSizeClassName,
    termsAndCondsText: defaultSubtitle,
  },
  advancedFileUpload: {
    id: "19",
    name: "advancedFileUpload",
    type: "advancedFileUpload",
    guid: defaultGuidString,
    workflowId: "",
    label: "Advanced File Upload",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
    fileTypes: defaultOptionsArray,
    fileTypeOptions: fileTypeOptions,
    validations: {
      required: { isRequired: false },
      advancedFileValidations: {
        [defaultOption.label]: {
          isRequired: false,
          availableFileTypes: fileTypeOptions.map((fileType) => fileType.value),
          message: `Allowed file types for ${defaultOption.label} are: {types}`,
        },
      },
      maxFileSize: {
        maxSize: 5,
        message:
          "This field must have a file size less than or equal to {maxSize} MB",
      },
    },
  },
  scale: {
    id: "20",
    name: "scale",
    type: "scale",
    guid: defaultGuidString,
    workflowId: "",
    label: "Scale",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
    columns: [
      { label: "Option 1", value: "Option 1" },
      { label: "Option 2", value: "Option 2" },
      { label: "Option 3", value: "Option 3" },
      { label: "Option 4", value: "Option 4" },
      { label: "Option 5", value: "Option 5" },
    ],
    rows: ["Question 1"],
  },
  stars: {
    id: "21",
    name: "stars",
    type: "stars",
    guid: defaultGuidString,
    workflowId: "",
    label: "Stars",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: halfSizeClassName,
    pageNumber: -1,
  },
  autoComplete: {
    id: "22",
    name: "autoComplete",
    type: "autoComplete",
    guid: defaultGuidString,
    workflowId: "",
    label: "Auto Complete",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
    externalApplication: "",
    sourceTable: "",
    searchableField: "",
    selectedDataverseTable: "",
    displayFields: [],
    displayFieldLabels: {},
    externalApplications: [
      { label: "Microsoft Dataverse", value: "Microsoft Dataverse" },
      { label: "Salesforce", value: "Salesforce" },
      { label: "Workday", value: "Workday" },
    ],
  },
  tableComponent: {
    id: "23",
    name: "tableComponent",
    type: "tableComponent",
    guid: defaultGuidString,
    workflowId: "",
    label: "TableComponent",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    alignment: alignmentOptions.column,
    pageNumber: -1,
    isFullWidth: false,
    sourceTable: "",
    searchableField: "",
    rowData: {},
  },
  payments: {
    id: "24",
    name: "payments",
    type: "payments",
    guid: defaultGuidString,
    workflowId: "",
    label: "Payments",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    description: "Write your product description here...",
    price: 0,
    modes: [
      { label: "Live Mode", value: "Live Mode" },
      { label: "Test Mode", value: "Test Mode" },
    ],
    currentMode: "Test Mode",
    currencies: [
      {
        label: "USD - United States Dollar",
        value: "USD - United States Dollar",
      },
      { label: "₹ - Indian Rupee", value: "₹ - Indian Rupee" },
      { label: "€ - Euro", value: "€ - Euro" },
    ],
    currentCurrency: "USD - United States Dollar",
    address: {
      name: "address",
      label: "",
      subtitle: defaultSubtitle,
      validations: { required: { isRequired: true } },
    },
    validations: { required: { isRequired: true }, canUserChangePrice: false },
  },
  address: {
    id: "31",
    name: "address",
    type: "address",
    guid: defaultGuidString,
    workflowId: "",
    label: "Address",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
  },
  accordion: {
    id: "30",
    name: "accordion",
    type: "accordion",
    guid: defaultGuidString,
    workflowId: "",
    label: "Accordion",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    expandable: false,
    accordionTabs: [
      {
        label: "Section 1",
        defaultExpand: false,
      },
    ],
  },
  employeeLookup: {
    id: "29",
    name: "employeeLookup",
    type: "employeeLookup",
    guid: defaultGuidString,
    workflowId: "",
    label: "Employee Lookup",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
    displayFields: [
      "employeeID",
      "effectiveTransferHireDate",
      "position",
      "department",
      "supervisor",
      "accountStatus",
      "telecommute",
      "hiringType",
      "employeeType",
      "phone",
      "email"
    ],
    displayFieldLabels: {
      employeeID: "Employee ID",
      effectiveTransferHireDate: "Effective Date",
      position: "Position",
      department: "Department",
      supervisor: "Supervisor",
      accountStatus: "Account Status",
      telecommute: "Telecommute",
      hiringType: "Hiring Type",
      employeeType: "Employee Type",
      phone: "Phone",
      email: "Email"
    },
  },
  vendorDetails: {
    id: "26",
    name: "vendorDetails",
    type: "vendorDetails",
    guid: defaultGuidString,
    workflowId: "",
    label: "Vendor Details",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
  },
  requestor: {
    id: "27",
    name: "requestors",
    type: "requestors",
    guid: defaultGuidString,
    workflowId: "",
    label: "Requestor fields",
    sublabel: ["Requestor", "Department", "Supervisor"],
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
  },
  heading: {
    id: "25",
    name: "heading",
    type: "heading",
    guid: defaultGuidString,
    workflowId: "",
    label: "Heading",
    alignment: "row",
    pageNumber: -1,
    subtitle: defaultHeaderForHeading,
    divClassName: halfSizeClassName,
  },
  jsTable: {
    id: "28",
    name: "jsTable",
    type: "jsTable",
    guid: defaultGuidString,
    workflowId: "",
    label: "Spreadsheet",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    alignment: alignmentOptions.column,
    pageNumber: "",
    isFullWidth: false,
    sourceTable: "",
    searchableField: "",
    rowData: {},
    ...ObjectLinkingProperties,
    isObjectLinking: false,
  },
  versionedFileUpload: {
    id: "32",
    name: "versionedFileUpload",
    type: "versionedFileUpload",
    guid: defaultGuidString,
    workflowId: "",
    label: "Versioned File Upload",
    subtitle: defaultSubtitle,
    alignment: alignmentOptions.column,
    divClassName: fullSizeClassName,
    pageNumber: -1,
    fileTypes: defaultOptionsArray,
    fileTypeOptions: fileTypeOptions,
    validations: {
      required: { isRequired: false },
      advancedFileValidations: {
        [defaultOption.label]: {
          isRequired: false,
          availableFileTypes: fileTypeOptions.map((fileType) => fileType.value),
          message: `Allowed file types for ${defaultOption.label} are: {types}`,
        },
      },
      maxFileSize: {
        maxSize: 5,
        message:
          "This field must have a file size less than or equal to {maxSize} MB",
      },
    },
  },
  userSuggestion: {
    id: "33",
    name: "userSuggestion",
    type: "userSuggestion",
    guid: defaultGuidString,
    alignment: alignmentOptions.column,
    workflowId: "",
    label: "User Suggestion",
    subtitle: defaultSubtitle,
    divClassName: halfSizeClassName,
  },
  objectLink: {
    // TODO: Update the metadata when implementing the ezDocs and ezSign modules.
    id: "34",
    name: "objectLink",
    type: "objectLink",
    guid: defaultGuidString,
    workflowId: "",
    label: "Object Link",
    subtitle: defaultSubtitle,
    divClassName: halfSizeClassName,
    alignment: alignmentOptions.column,
    pageNumber: -1,
    ...ObjectLinkingProperties,
    allowMultiSelectForResults: false,
    validations: {
      required: { isRequired: false },
    },
  },
  objectLinkGrid: {
    id: "35",
    name: "objectLinkGrid",
    type: "objectLinkGrid",
    guid: defaultGuidString,
    workflowId: "",
    label: "Object Link Grid",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    alignment: alignmentOptions.column,
    pageNumber: -1,
    ...ObjectLinkingProperties,
    dataTableColumns: [],
    isObjectLinking: true,
    additionalColumns: [],
    rows: [{}],
    validations: {
      required: { isRequired: false },
    },
  },
  grid: {
    // TODO: Update the metadata when implementing the ezDocs and ezSign modules.
    id: "36",
    name: "grid",
    type: "grid",
    guid: defaultGuidString,
    workflowId: "",
    label: "Grid",
    subtitle: defaultSubtitle,
    divClassName: fullSizeClassName,
    alignment: alignmentOptions.column,
    pageNumber: -1,
    ...ObjectLinkingProperties,
    dataTableColumns: [],
    selectedDataTableColumns: [],
    isSpreadsheet:false,
    isObjectLinking: true,
    additionalColumns: [],
    rows: [{}],
    validations: {
      required: { isRequired: false },
    },
  },
};
