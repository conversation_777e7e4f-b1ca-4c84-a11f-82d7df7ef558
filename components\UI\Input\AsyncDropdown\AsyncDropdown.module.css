.container {
  position: relative;
  width: 100%;
}

.optionsContainer {
  z-index: 1000;
  position: absolute;
  width: inherit;
  max-width: 40vh;
  list-style-type: none;
  padding: 0.3rem 0.3rem 0 0;
  margin: 0;
  background-color: white;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 3px;
  height: 210px;
  overflow-y: scroll;
}

.inputContainer {
  display: flex;
  align-items: center;
}

.option {
  margin: 0;
  list-style-type: none;
  padding: 0.4rem 0.6rem;
  cursor: pointer;
  font-size: 14px;
  border: 0 none;
  color: #004990;
  border-bottom: 1px solid #ccdae9;
  padding-bottom: 13px;
  margin-bottom: -8px;
  padding-top: 20px;
  font-family: 'Open Sans';
  font-weight: 600;
  text-transform: capitalize;
  font-size: 14px;
}

.option:hover {
  background-color: var(--primary-bg-darkCerulean-10);
}

/* Need to apply a style whewn option is selected */
.option.selected {
  background-color: var(--primary-bg-darkCerulean-20);
}

.paginator {
  padding: 0 !important;
  padding-right: 0.3rem !important;
  position: sticky;
  bottom: 0;
}

.styledplaceholder::placeholder {
  padding-right: 3rem;
  color: var(--primary-bg-darkCerulean);
}

.cursor {
  cursor: pointer;
  margin-left: -2rem;
}