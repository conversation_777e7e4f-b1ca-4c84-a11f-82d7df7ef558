import { useContext } from 'react'
import { UserImageContext } from '../../../public/ContextProviders/UserImageContextProvider'
import Image from 'next/image'
import { MicrosoftEntraIdContext } from '../../../public/ContextProviders/MicrosoftEntraIdContextProvider'
import clsx from 'clsx'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import UserProfile from '../../../svg/Common/common_DP.svg'
import blueProfile from '../../../svg/metronic/blue_profile.svg'
import useUserImageByObjectId from '../../../hooks/useUserImageByObjectId'

import styles from './UserProfileImage.module.css'
import { useRouter } from 'next/router'

export const UserProfileImage = ({
  width = 150,
  height = 150,
  onClick,
  objectId,
  fromLmsBanner = false,
}) => {
  const userImageContext = useContext(UserImageContext)
  const { userImageData } = useUserImageByObjectId(objectId)
  const router = useRouter()

  const imageSource = objectId
    ? userImageData
    : router.pathname.includes('LeadGeneration')
    ? blueProfile
    : userImageContext?.userImage

  return (
    <Image
      className={clsx(
        fromLmsBanner ? styles.userImageLMS : styles.userImage,
        onClick && styles.clickable
      )}
      src={imageSource ?? UserProfile}
      width={width}
      height={height}
      alt="Profile Image"
      onClick={onClick}
    />
  )
}

export const UserProfileInfoLabel = () => {
  const userProfileEntraId = useContext(MicrosoftEntraIdContext)

  return (
    <>
      <UserProfileDisplayNameLabel />
      <UserProfileJobTitleAndRoleLabel />
    </>
  )
}

export const UserProfileDisplayNameLabel = () => {
  const userProfileEntraId = useContext(UserProfileContext)

  return (
    <h3 className={styles.displayName}>
      {userProfileEntraId?.displayName ?? '-'}
    </h3>
  )
}

export const UserProfileJobTitleAndRoleLabel = () => {
  const userProfileEntraId = useContext(MicrosoftEntraIdContext)

  return (
    <div className={styles.profileTitle}>
      {userProfileEntraId?.jobTitle ?? '-'} |{' '}
      {userProfileEntraId?.role?.name ?? '-'}{' '}
    </div>
  )
}
