import { createContext, useEffect, useState } from 'react'
import { SolutionIds } from '../../../src/contants'
import { useRouter } from 'next/router'

export const LayoutContext = createContext(undefined)

export const LayoutContextProvider = ({ children }) => {
  const router = useRouter()
  const [layout, setLayout] = useState({})
  const [solutionId, setSolutionId] = useState(SolutionIds.Papyrus)

  useEffect(() => {
    const path = router.pathname
    switch (true) {
      case path.includes('LeadGeneration'):
        setSolutionId(11)
        break
      case path.includes('LMS'):
        setSolutionId(11)
        break
      case path.includes('InstructorLMS'):
        setSolutionId(12)
        break
      case path.includes('ITSM'):
        setSolutionId(14)
        break
      default:
        setSolutionId(1)
        break
    }
  }, [router])

  return <LayoutContext.Provider value={{ solutionId, setSolutionId }}>{children}</LayoutContext.Provider>
}
