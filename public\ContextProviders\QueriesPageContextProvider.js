import { useQuery } from "@tanstack/react-query"
import { createContext, useContext, useEffect, useState, useRef } from "react"
import { formBuilderApiRequest } from "../../src/msalConfig"
import { getAccessTokenForScopeSilent } from "../../src/GetAccessTokenForScopeSilent"
import { Guid } from "js-guid"
import { Toast } from "primereact/toast"
import { useFormDefinitionMasterFilter } from "../../hooks/useFormDefinitionMasterFilter"
import { useFetchQuery } from "../../hooks/Queries/useFetchQuery"
import { useQueryManagement } from "../../hooks/Queries/useQueryManagement"
import { usePapyrusDriveValues } from "../../hooks/Queries/usePapyrusDriveValues"
import { QueriesModules } from "../../src/contants"

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const QueriesPageContext = createContext(undefined)

export const QueriesPageContextProvider = ({ id, solutionId, children }) => {
  // Should be given as props to the context provider
  const toast = useRef(null)

  const [queryResult, setQueryResult] = useState([])
  const [hasUserDecidedOnConditions, setHasUserDecidedOnConditions] = useState(false);
  const [shouldAutoRun, setShouldAutoRun] = useState(true)
  const [loading, setLoading] = useState(false)

  const { queryInfo, setQueryInfo, updateQueryInfoWithResponse } = useQueryInfo()
  const { sqlQuery, setSqlQuery } = useSqlQuery()
  const { dataQuery: formWorkflowDataQuery, setDataQuery: setFormWorkflowDataQuery, } = useDataQuery() // finalizedDataQuery, setFinalizedDataQuery <- These do not seem to be used. Check w/ Yibran before deleting. - Alex
  const { dataQuery: papyrusDriveDataQuery, setDataQuery: setPapyrusDriveDataQuery } = useDataQuery()
  const { saveQuery } = useQueryManagement()
  const { formDefinitionMasterFilter, setFormDefinitionMasterFilter, updateFormDefinitionMasterFilterWithResponse } = useFormDefinitionMasterFilter()
  const {
    state: papyrusDriveState,
    setPapyrusDriveState,
    setPapyrusDriveDepartment,
    setPapyrusDriveSubDepartment,
    setPapyrusDriveTeam,
    setPapyrusDriveDocumentType,
    setPapyrusDriveDocument,
    setPapyrusDriveRoutingStatus
  } = usePapyrusDriveValues()
  const { fetchQuery, runQuery, runDMSQuery, isLoadingRunQuery } = useFetchQuery()

  const tempValueForPapyrusDrive = { module: QueriesModules.PapyrusDrive, formDefinitionMaster: {} }
  // I'm just putting this object^ here so that we don't get an error from the updateFormDefinitionMasterFilterWithResponse
  // function. We can delete this after refactoring.

  // This useQuery is for loading the saved query if it exists.
  useQuery(
    ['queryData', id],
    () => fetchQuery(id), // Fetch saved query data
    {
      enabled: !!id, // Only run if id is available
      refetchInterval: shouldAutoRun ? 200 : false,
      onSuccess: (fetchedData) => {
        const { module: fetchedDataModule } = fetchedData || {}
        updateQueryInfoWithResponse(fetchedData)

        switch (fetchedDataModule) {
          case QueriesModules.FormWorkflow:
            updateFormDefinitionMasterFilterWithResponse(fetchedData)
            setSqlQuery(fetchedData.queryMetadata.metadata.sqlQuery)
            setFormWorkflowDataQuery(fetchedData.queryMetadata.metadata.mongoQuery)
            break
          case QueriesModules.PapyrusDrive:
            updateFormDefinitionMasterFilterWithResponse(tempValueForPapyrusDrive)
            setPapyrusDriveDataQuery(fetchedData.queryMetadata.metadata.mongoQuery)
            setPapyrusDriveState(fetchedData.queryMetadata.metadata.sqlQuery)
            break
        }

        // Assume the user decided on conditions since they saved a query.
        setHasUserDecidedOnConditions(true)

        // Auto-run the query when the page loads for the first time.
        if (shouldAutoRun) {
          setShouldAutoRun(false) // Ensure it runs only once
          if (fetchedData.module === QueriesModules.FormWorkflow) {
            runQuery({
              setQueryResult,
              sqlQuery: fetchedData.queryMetadata.metadata.sqlQuery,
              dataQuery: fetchedData.queryMetadata.metadata.mongoQuery,
            })
          } else if (fetchedData.module === QueriesModules.PapyrusDrive) {
            runDMSQuery({
              setQueryResult,
              sqlQuery: fetchedData.queryMetadata.metadata.sqlQuery,
              dataQuery: fetchedData.queryMetadata.metadata.mongoQuery,
              papyrusDriveState,
              hasUserDecidedOnConditions
            })
          }
        }
      }
    }
  )

  const fetchFormMetadata = async () => {
    try {
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)

      const response = await fetch(`${api}FormMetadata/${sqlQuery.formDefinition.metadataId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': '*/*',
        }
      })

      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error while fetching form metadata', error)
      return {}
    }
  }

  const { data: formMetadata, isLoading: loadingFormMetadata } = useQuery({
    queryKey: ["formMetadataId", sqlQuery.formDefinition.metadataId],
    queryFn: fetchFormMetadata,
    enabled: sqlQuery?.formDefinition?.metadataId != null,
    refetchInterval: false,
    staleTime: 0,
    initialData: {},
  })

  const checkIfUserBuiltQuery = () => {
    switch (formDefinitionMasterFilter.module) {
      case QueriesModules.FormWorkflow:
        return !(formDefinitionMasterFilter?.formDefinitionMaster && formDefinitionMasterFilter?.department && hasUserDecidedOnConditions)
      case 1:
        return !(papyrusDriveState?.department && papyrusDriveState?.document?.length > 0 && hasUserDecidedOnConditions)
      default:
        return true
    }
  }

  const handleRunQuery = (moduleId) => {
    switch (moduleId) {
      case QueriesModules.FormWorkflow:
        runQuery({
          setQueryResult,
          sqlQuery,
          dataQuery: formWorkflowDataQuery
        })
        break
      case QueriesModules.PapyrusDrive:
        runDMSQuery({
          setQueryResult,
          sqlQuery,
          dataQuery: papyrusDriveDataQuery,
          papyrusDriveState,
        })
        break
      default:
        return
    }
  }

  const postSaveQuery = async (e) => {
    setLoading(true)
    e.preventDefault()
    try {
      const { name, queryType } = queryInfo

      const data = await saveQuery('POST', `${api}Query`, {
        name: name,
        queryType: queryType,
        sqlQuery: formDefinitionMasterFilter.module === QueriesModules.FormWorkflow ? sqlQuery : papyrusDriveState, // This will need to be updated once we have more than two modules in Queries. - Alex
        mongoQuery: formDefinitionMasterFilter.module === QueriesModules.FormWorkflow ? formWorkflowDataQuery : papyrusDriveDataQuery, // This will need to be updated once we have more than two modules in Queries. - Alex
        module: formDefinitionMasterFilter.module,
        formDefinitionId: sqlQuery.formDefinition.id,
      })

      updateQueryInfoWithResponse(data)
      toast.current.show({ severity: 'success', summary: 'Success', detail: 'Query Saved', life: 3000 })
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  const updateSavedQuery = async (e) => {
    setLoading(true)
    e.preventDefault()
    try {
      const { name, queryType } = queryInfo

      await saveQuery('PUT', `${api}Query/${queryInfo.id}`, {
        name: name,
        queryType: queryType,
        sqlQuery: sqlQuery,
        mongoQuery: formDefinitionMasterFilter.module === QueriesModules.FormWorkflow ? formWorkflowDataQuery : papyrusDriveDataQuery,
        // Need to update this^ once we have more than two modules. - Alex
        module: formDefinitionMasterFilter.module,
        formDefinitionId: sqlQuery.formDefinition.id,
      })

      toast.current.show({ severity: 'success', summary: 'Success', detail: 'Query Updated', life: 3000 })
    } catch (error) {
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <QueriesPageContext.Provider value={{
      sqlQuery, setSqlQuery, runQuery, runDMSQuery, isLoadingRunQuery, queryResult,
      setQueryResult, formWorkflowDataQuery, setFormWorkflowDataQuery, formMetadata, loadingFormMetadata,
      queryInfo, setQueryInfo, postSaveQuery, checkIfUserBuiltQuery, formDefinitionMasterFilter,
      setFormDefinitionMasterFilter, updateSavedQuery, hasUserDecidedOnConditions, setHasUserDecidedOnConditions, 
      papyrusDriveState, setPapyrusDriveDepartment, setPapyrusDriveSubDepartment, setPapyrusDriveTeam, 
      setPapyrusDriveDocumentType, setPapyrusDriveDocument, setPapyrusDriveRoutingStatus, handleRunQuery, loading,
      papyrusDriveDataQuery, setPapyrusDriveDataQuery, solutionId
      // finalizedDataQuery, setFinalizedDataQuery,
    }}>
      <Toast ref={toast} />
      {children}
    </QueriesPageContext.Provider>
  )

}

export const useQueriesPageContext = () => {
  const context = useContext(QueriesPageContext)

  if (context === undefined) {
    throw new Error('useQueriesPageContext must be used within a QueriesPageContextProvider')
  }

  return {
    sqlQuery: context.sqlQuery,
    setSqlQuery: context.setSqlQuery,
    runQuery: context.runQuery,
    runDMSQuery: context.runDMSQuery,
    isLoadingRunQuery: context.isLoadingRunQuery,
    formWorkflowDataQuery: context.formWorkflowDataQuery,
    setFormWorkflowDataQuery: context.setFormWorkflowDataQuery,
    papyrusDriveDataQuery: context.papyrusDriveDataQuery,
    setPapyrusDriveDataQuery: context.setPapyrusDriveDataQuery,
    formMetadata: context.formMetadata,
    loadingFormMetadata: context.loadingFormMetadata,
    queryResult: context.queryResult,
    setQueryResult: context.setQueryResult,
    queryInfo: context.queryInfo,
    setQueryInfo: context.setQueryInfo,
    postSaveQuery: context.postSaveQuery,
    checkIfUserBuiltQuery: context.checkIfUserBuiltQuery,
    formDefinitionMasterFilter: context.formDefinitionMasterFilter,
    setFormDefinitionMasterFilter: context.setFormDefinitionMasterFilter,
    updateSavedQuery: context.updateSavedQuery,
    hasUserDecidedOnConditions: context.hasUserDecidedOnConditions,
    setHasUserDecidedOnConditions: context.setHasUserDecidedOnConditions,
    module: context.formDefinitionMasterFilter.module,
    handleRunQuery: context.handleRunQuery,
    loading: context.loading,
    solutionId: context.solutionId
  }
}

export const useQueriesPagePapyrusDriveModule = () => {
  const context = useContext(QueriesPageContext)

  if (context === undefined) {
    throw new Error('useQueriesPagePapyrusDriveModule must be used within a QueriesPageContextProvider')
  }

  return {
    papyrusDriveState: context.papyrusDriveState,
    setPapyrusDriveDepartment: context.setPapyrusDriveDepartment,
    setPapyrusDriveSubDepartment: context.setPapyrusDriveSubDepartment,
    setPapyrusDriveTeam: context.setPapyrusDriveTeam,
    setPapyrusDriveDocumentType: context.setPapyrusDriveDocumentType,
    setPapyrusDriveDocument: context.setPapyrusDriveDocument,
    setPapyrusDriveRoutingStatus: context.setPapyrusDriveRoutingStatus
  }
}

const useSqlQuery = () => {
  const [sqlQuery, setSqlQuery] = useState(createInitialSqlQueryInfo())

  return { sqlQuery, setSqlQuery }
}

// Renamed this to useDataQuery instead of mongoQueries for consistency and clarity, can be used for DMS Queries even though its not using mongoDB. - Yibran
const useDataQuery = () => {
  const [dataQuery, setDataQuery] = useState(createInitialDataQueryInfo())

  const [finalizedDataQuery, setFinalizedDataQuery] = useState(null)

  return { dataQuery, setDataQuery, finalizedDataQuery, setFinalizedDataQuery }
}

const useQueryInfo = () => {
  const [queryInfo, setQueryInfo] = useState({
    id: null,
    name: null,
    createdDate: null,
    updatedDate: null,
    createdByDisplayName: null,
    createdByEmail: null,
    queryType: 1,
    metadataId: null,
  })

  const updateQueryInfoWithResponse = (data) => {
    const { id, name, createdDate, updatedDate, createdByDisplayName, createdByEmail, queryType, metadataId } = data

    setQueryInfo({
      id: id,
      name: name,
      createdDate: createdDate,
      updatedDate: updatedDate,
      createdByDisplayName: createdByDisplayName,
      createdByEmail: createdByEmail,
      queryType: queryType,
      metadataId: metadataId,
    })
  }

  return { queryInfo, setQueryInfo, updateQueryInfoWithResponse }
}

export const createInitialSqlQueryInfo = () => ({
  formDefinition: {
    id: null,
    metadataId: null
  },
  submissionDateFrom: null,
  submissionDateTo: null,
  formStatus: null,
})

export const clearSqlQueryExceptFormAndStatus = (currentSqlQuery) => ({
  formDefinition: currentSqlQuery.formDefinition || {
    id: null,
    metadataId: null
  },
  formStatus: currentSqlQuery.formStatus || null,
  submissionDateFrom: null,
  submissionDateTo: null,
})

export const createInitialDataQueryInfo = () => ({
  [Guid.newGuid().StringGuid]: createDefaultConditionGroup()
})

export const defaultCondition = {
  field: { guid: "00000000-0000-0000-0000-000000000000", name: '', label: '' },
  operand: '',
  value: '',
}

export const createDefaultConditionGroup = (defaulCondition = {}) => ({
  operand: 'AND',
  conditions: {
    [Guid.newGuid().toString()]: { ...defaulCondition },
  },
})