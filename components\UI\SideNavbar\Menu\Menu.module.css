.menu {
  list-style-type: none;
  position: relative;
  width: 400px;
  /* height: 95vh; */
  /* max-height: 100%; */
  transition: width 0.3s ease-in-out;
  /* margin: 1rem 0 1rem 1rem; */
  overflow-y: scroll;
  overflow: visible;
  margin-right: -0.2%;
}

.menu::-webkit-scrollbar-thumb {
  background-color: #f7f9fb;
}

.container {
  color: white;
  background-color: var(--sidebar-bg);
  height: 100%;
  border-radius: 5px;
  position: relative;
  top: 0;
  left: 0;
  width: 98%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.subMenu {
  display: block;
  list-style-type: none;
  margin-top: 25px;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 0px;
}

.collapsed {
  width: 80px;
}

.collapsedContainer {
  border-radius: 10px;
}

.toggleIcon {
  position: relative;
  display: flex;
  padding: 10px 0px 10px 20px;
  justify-content: flex-end;
  align-items: center;
  overflow: visible;
  height: 50px;
  width: 100%;
}

.icon {
  font-size: 25px;
}

.headTxt {
  font-size: 20px;
  left: 1rem;
  position: relative;
}

.header {
  display: flex;
  justify-content: space-between;
  padding-top: 1.5rem;
  padding-bottom: 1rem;
}

@keyframes spinToCollapsed {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(180deg);
  }
}

@keyframes spinToExpanded {
  from {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(0deg);
  }
}

.rotateToCollapsed {
  animation: spinToCollapsed 0.3s forwards;
}

.rotateToExpanded {
  animation: spinToExpanded 0.3s forwards;
}

/* CSS classes that start with "builders" are for the side nav menus in the form builder and workflow builder */

.buildersContainer {
  color: white;
  background-color: var(--sidebar-bg);
  height: 99%;
  border-radius: 5px;
  position: relative;
  top: 3px;
  left: 4px;
  width: 100%;
  overflow: auto;
}

.buildersToggleIcon {
  display: flex;
  justify-content: flex-end;
}

.buildersSubMenu {
  display: block;
  list-style-type: none;
  margin-block-start: 0em;
  margin-block-end: 0em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  padding-inline-start: 0px;
  width: 315px;
}

.buildersCollapsed {
  width: 60px;
}

.menuFooter {
  display: flex;
  /* justify-content: center; */
  align-items: center;
  font-size: 14px;
  color: #fff;
  margin-top: auto;
  margin-left: 1.5rem;
}

.menuFooterITSM {
  display: flex;
  flex-direction: column;
  margin-top: auto;
  margin-left: 1.5rem;
  width: 50%;
}

.menuFooterITSM h3 {
  margin: 10px 0 0 0;
  color: #515151;
  font-size: 14px;
  font-weight: 700;
}

.menuFooterITSM p {
  margin: 0 0 10px 0;
  color: #515151;
  font-size: 12px;
  font-weight: 400;
}

.ITSMButtonOutline {
  padding: 10px 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;
  color: #333;
  font-family: sans-serif;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  cursor: pointer;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.ITSMButtonOutline:hover {
  background-color: #f9f9f9;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.05);
}

.collapsed .menuFooter {
  margin-left: 0.6rem;
}

.collapsed .menuFooter p {
  width: 5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
}
.metronicContainer {
  background-color: #fff;
  height: 99%;
  border-radius: 5px;
  position: relative;
  top: 3px;
  left: 4px;
  width: 98%;
  overflow: visible;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #5151511a;
}
