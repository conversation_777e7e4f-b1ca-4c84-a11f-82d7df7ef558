import React from "react";
import LexicalEditor from "../../../LexicalEditor/LexicalEditor";
import ColumnSizeDropdowm from "../ColumnSizeDropdown/ColumnSizeDropdowm";
import SettingsMenu from "../SettingsMenu/SettingsMenu";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";
import SelectInput from "../../../UI/Input/SelectInput/SelectInput";
import Modal from "../../../UI/Modal/Modal";
import { SettingsLabel } from "../UI/SettingsLabel/SettingsLabel";
import { useState, useEffect } from "react";
import { useMsalAuthentication } from "@azure/msal-react";
import { InteractionType } from "@azure/msal-browser";
import { formBuilderApiRequest } from "../../../../src/msalConfig";
import { SolutionIds } from "../../../../src/contants";
import { SourceForm } from "../../SharedComponents/ObjectLinking/SourceForm";
import { PrimarySearchField } from "../../SharedComponents/ObjectLinking/PrimarySearchField";
import { SelectModule } from "../../SharedComponents/ObjectLinking/SelectModule";
import { useFetchSourceForm } from "../../../../hooks/useFetchSourceForm";
import { AsyncResultFieldsDropdown } from "../../../UI/Input/AsyncDropdown/AsyncResultFieldsDropdown";
import { AddOptionButton } from "../UI/OptionEditor/OptionEditor";
import { InputText } from "primereact/inputtext";
import { Checkbox } from "primereact/checkbox";
import { Toggle } from "../UI/Toggle/Toggle";
import { AutoComplete } from "primereact/autocomplete";

import SwimLaneStyles from "../../../../styles/BuilderSwimLaneStyles/SettingsMenu.module.css";
import { useApi } from "../../../../hooks/useApi";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API;

const newColumnObject = {
  title: "",
  dataType: "",
  isRequired: false,
};

const dataTypeOptions = [
  { label: "Text", value: "text" },
  { label: "Number", value: "number" },
  { label: "Currency", value: "currency" },
  { label: "Percentage", value: "percentage" },
  { label: "Selectable Date", value: "selectableDate" },
  { label: "Today's Date", value: "todayDate" },
  { label: "Dropdown", value: "dropdown" },
  { label: "Multiselect", value: "multiselect" },
];

// Constants for data type validation
const NUMERIC_DATA_TYPES = ["number", "currency", "percentage"];
const isNumericDataType = (dataType) => NUMERIC_DATA_TYPES.includes(dataType);

const getNumericColumnNames = (inputs) => {
  const columns = [];

  // Add result fields
  if (inputs?.resultFields) {
    inputs.resultFields.forEach((field) => {
      if (
        field.elementData?.type === "number" ||
        field.elementData?.type === "currency"
      ) {
        columns.push(field.name);
      }
    });
  }

  // Add additional columns
  if (inputs?.additionalColumns) {
    inputs.additionalColumns.forEach((column) => {
      if (isNumericDataType(column.dataType)) {
        columns.push(column.title);
      }
    });
  }

  return columns;
};

// TODO: Use the solutionId to filter object linking form definitions by solution.
export default function ObjectLinkGridDialog({
  hideDialog,
  inputs,
  assignValuesNested,
  handleInputChange,
  solutionId = SolutionIds.Papyrus,
}) {
  // TODO: Consider splitting this large component into smaller, focused components:
  // 1. ColumnManager - Handles column operations and configuration
  // 2. FooterFieldManager - Handles footer field operations
  // 3. CalculationManager - Handles formula calculations and validation
  // 4. ModalManager - Handles modal state and operations
  // 5. FormManager - Handles form state and validation

  // TODO: Extract validation logic into separate utility functions
  // This would make the code more maintainable and testable

  // TODO: Consider using a reducer pattern for complex state management
  // This would make state updates more predictable and easier to debug

  // TODO: Consider using custom hooks for:
  // - Form definition fetching
  // - Column management
  // - Footer field management
  // - Calculation validation
  // - Modal state management

  // TODO: Add proper error boundaries for better error handling
  // This would prevent the entire component from crashing

  // TODO: Consider using React.memo for performance optimization
  // This would prevent unnecessary re-renders of child components
  console.log("inputs(objectLinkGrid):", inputs);
  const [sourceFormOptions, setSourceFormOptions] = useState([]);
  const [sourceFormOptionsLabels, setSourceFormOptionsLabels] = useState([]);
  const [showOptions, setShowOptions] = useState(false);
  const [resultFieldOptions, setResultFieldOptions] = useState([]);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isCalculateModalVisible, setIsCalculateModalVisible] = useState(false);
  const [currentCalculationIndex, setCurrentCalculationIndex] = useState(null);
  const [calculationFormula, setCalculationFormula] = useState("");
  const [calculationError, setCalculationError] = useState("");
  const [isFooterModalVisible, setIsFooterModalVisible] = useState(false);
  const [footerFields, setFooterFields] = useState({});
  const [currentFooterIndex, setCurrentFooterIndex] = useState(null);
  const [footerFormula, setFooterFormula] = useState("");
  const [footerError, setFooterError] = useState("");
  const [selectedColumn, setSelectedColumn] = useState("");
  const { createTargetObject } = useUtilityFunctions();
  const { fetchFormDefinition } = useFetchSourceForm();
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  );
  const [filteredColumns, setFilteredColumns] = useState([]);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [currentWord, setCurrentWord] = useState("");

  const { callApi, loading } = useApi();

  useEffect(() => {
    // To initialize the source form options.
    fetchFormDefinition({
      acquireToken,
      setSourceFormOptions,
      setSourceFormOptionsLabels,
      solutionId,
    });
  }, []);

  useEffect(() => {
    let tempColumns = JSON.parse(JSON.stringify(inputs?.resultFields));
    if (!tempColumns) return;
    tempColumns.unshift(
      transformPrimarySearchField(inputs?.primarySearchField)
    );

    setResultFieldOptions(tempColumns);
  }, [inputs?.primarySearchField]);

  if (!inputs) {
    // Used to prevent an error when using the undo functionality.
    hideDialog();
    return;
  }

  const handleFocus = () => {
    setShowOptions(true);
  };

  const handleOutsideClick = () => {
    setShowOptions(false);
  };

  const handleValueChange = (newValue) => {
    handleInputChange(
      createTargetObject(`${inputs?.guid}.resultFields`, newValue)
    );
  };

  const handleColumnChange = (e, option) => {
    const copy = JSON.parse(JSON.stringify(resultFieldOptions));

    const currentElement = copy.find(
      (item) => item.columnValue === option.columnValue
    );
    const swapElement = copy.find(
      (item) => item.columnValue === e.target.value
    );

    const currentElementIndex = copy.findIndex(
      (item) => item.columnValue === option.columnValue
    );
    const swapElementIndex = copy.findIndex(
      (item) => item.columnValue === e.target.value
    );

    if (currentElement && swapElement) {
      swapElement.columnValue = currentElement.columnValue;
      currentElement.columnValue = e.target.value;

      copy[currentElementIndex] = swapElement;
      copy[swapElementIndex] = currentElement;
    } else if (currentElement) {
      currentElement.columnValue = e.target.value;
    }

    handleInputChange(
      createTargetObject(
        `${inputs?.guid}.resultFields`,
        filterElementsBySelectedResultFields(inputs?.resultFields, copy)
      )
    );
    setResultFieldOptions(copy);
  };

  const transformPrimarySearchField = (inputObject) => {
    return {
      elementData: { ...inputObject },
      columnValue: "A",
      name: inputObject.label,
      id: inputObject.guid,
      columnList: ["A", "B", "C", "D"],
    };
  };

  const filterElementsBySelectedResultFields = (
    selectedResultFields,
    newResultFields
  ) => {
    const set = new Set(selectedResultFields.map((item) => item.id));
    return newResultFields.filter((item) => set.has(item.id));
  };

  const addNewColumn = () => {
    const temp = JSON.parse(
      JSON.stringify(inputs?.additionalColumns ? inputs.additionalColumns : [])
    );

    temp.push(newColumnObject);

    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );
  };

  const handleColumnTitleChange = (e, index) => {
    const temp = JSON.parse(JSON.stringify(inputs.additionalColumns));
    temp[index].title = e.target.value;
    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );
  };

  const handleColumnDataTypeChange = (e, index) => {
    // Input validation
    if (!inputs?.additionalColumns || index < 0 || index >= inputs.additionalColumns.length) {
      console.error('Invalid column index or additionalColumns not found');
      return;
    }

    const newDataType = e.target.value;
    const updatedColumns = [...inputs.additionalColumns];
    const currentColumn = { ...updatedColumns[index] };
    
    // Check if we're transitioning from numeric to non-numeric type
    const wasNumeric = isNumericDataType(currentColumn.dataType);
    const willBeNumeric = isNumericDataType(newDataType);
    
    // Clean up calculation properties when transitioning from numeric to non-numeric
    if (wasNumeric && !willBeNumeric && currentColumn.hasCalculate) {
      currentColumn.hasCalculate = false;
      currentColumn.calculationFormula = "";
    }
    
    currentColumn.dataType = newDataType;
    updatedColumns[index] = currentColumn;
    
    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, updatedColumns)
    );
  };

  const handleColumnRequiredChange = (e, index) => {
    const temp = JSON.parse(JSON.stringify(inputs.additionalColumns));
    temp[index].isRequired = e.checked;
    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );
  };

  const handleCalculateChange = (e, index) => {
    const temp = JSON.parse(JSON.stringify(inputs.additionalColumns));
    temp[index].hasCalculate = e.value;
    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );
  };

  const addNewDropdownOption = (index) => {
    const { additionalColumns } = inputs;
    const temp = JSON.parse(JSON.stringify(additionalColumns));
    const newDropdownOption = { label: "Option", value: "Option" };

    if (!temp[index]?.dropdownOptions) {
      temp[index].dropdownOptions = [newDropdownOption];
    } else {
      temp[index].dropdownOptions.push(newDropdownOption);
    }

    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );
  };

  const handleDropdownOptionChange = (e, columnsIndex, dropdownIndex) => {
    const { additionalColumns } = inputs;
    const temp = JSON.parse(JSON.stringify(additionalColumns));

    if (temp[columnsIndex]?.dropdownOptions) {
      temp[columnsIndex].dropdownOptions[dropdownIndex] = {
        label: e.target.value,
        value: e.target.value,
      };
    }

    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );
  };

  const handleEditableChange = (columnType, index, isEditable) => {
    let updatedData;

    if (columnType === "primarySearchField") {
      updatedData = {
        ...inputs.primarySearchField,
        editable: isEditable,
      };
      handleInputChange(
        createTargetObject(`${inputs?.guid}.primarySearchField`, updatedData)
      );
    } else if (columnType === "resultFields") {
      const updatedResultFields = [...inputs.resultFields];
      updatedResultFields[index] = {
        ...updatedResultFields[index],
        editable: isEditable,
      };
      handleInputChange(
        createTargetObject(`${inputs?.guid}.resultFields`, updatedResultFields)
      );
    } else if (columnType === "additionalColumns") {
      const updatedAdditionalColumns = [...inputs.additionalColumns];
      updatedAdditionalColumns[index] = {
        ...updatedAdditionalColumns[index],
        editable: isEditable,
      };
      handleInputChange(
        createTargetObject(
          `${inputs?.guid}.additionalColumns`,
          updatedAdditionalColumns
        )
      );
    }
  };

  const validateCalculationFormula = (formula) => {
    // Get all available numeric column names
    const availableColumns = [
      // Filter resultFields for numeric types
      ...(inputs?.resultFields
        ?.filter(
          (field) =>
            field.elementData?.type === "number" ||
            field.elementData?.type === "currency"
        )
        .map((field) => field.name) || []),

      // Filter additionalColumns for numeric types
      ...(inputs?.additionalColumns
        ?.filter(
          (column) =>
            column.dataType === "number" ||
            column.dataType === "currency" ||
            column.dataType === "percentage"
        )
        .map((column) => column.title) || []),
    ].filter(Boolean);

    // Check for balanced parentheses (excluding those in column names)
    let parenthesesCount = 0;
    let inColumnName = false;
    for (const char of formula) {
      if (char === "(" && !inColumnName) parenthesesCount++;
      if (char === ")" && !inColumnName) parenthesesCount--;
      if (char === " ") inColumnName = false;
      if (char === "(" && inColumnName) inColumnName = true;
      if (parenthesesCount < 0) {
        return "Unbalanced parentheses in formula.";
      }
    }
    if (parenthesesCount !== 0) {
      return "Unbalanced parentheses in formula.";
    }

    // Check for valid characters (allowing spaces, letters, numbers, operators, parentheses, and %)
    const validChars = /^[a-zA-Z0-9\+\-\*\/\(\)\s%]+$/;
    if (!validChars.test(formula)) {
      // Find invalid characters for better error message
      const invalidChars = [
        ...new Set(formula.split("").filter((char) => !validChars.test(char))),
      ];
      return `Invalid characters in formula: ${invalidChars.join(
        ", "
      )}. Only letters, numbers, spaces, +, -, *, /, %, and parentheses are allowed.`;
    }

    // Check for incomplete expressions (operators at the end)
    const trimmedFormula = formula.trim();
    if (/[\+\-\*\/]$/.test(trimmedFormula)) {
      return "Formula cannot end with an operator.";
    }

    // Check for consecutive operators
    if (/[\+\-\*\/]\s*[\+\-\*\/]/.test(trimmedFormula)) {
      return "Formula cannot have consecutive operators.";
    }

    // Check for operators at the start (except minus for negative numbers)
    if (/^[\+\*\/]/.test(trimmedFormula)) {
      return "Formula cannot start with an operator (except minus for negative numbers).";
    }

    // First, replace all column names with placeholders to protect them
    const columnPlaceholders = {};
    let placeholderCount = 0;
    const formulaWithPlaceholders = availableColumns.reduce(
      (acc, columnName) => {
        const placeholder = `__COL${placeholderCount}__`;
        columnPlaceholders[placeholder] = columnName;
        placeholderCount++;
        return acc.replace(
          new RegExp(columnName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
          placeholder
        );
      },
      formula
    );

    // Now split on operators and parentheses to get the parts
    const parts = formulaWithPlaceholders
      .split(/[\+\-\*\/\(\)]/)
      .map((part) => part.trim())
      .filter((part) => part && !/^\d+%?$/.test(part));

    // Convert placeholders back to column names
    const columnNames = parts.map((part) => {
      if (part in columnPlaceholders) {
        return columnPlaceholders[part];
      }
      return part;
    });

    // Check if all parts are valid column names
    const invalidColumns = columnNames.filter(
      (name) => !availableColumns.includes(name)
    );
    if (invalidColumns.length > 0) {
      return `Invalid column names: ${invalidColumns.join(
        ", "
      )}. Only numeric columns can be used in calculations.`;
    }

    return "";
  };

  const handleSaveCalculation = () => {
    const error = validateCalculationFormula(calculationFormula);
    if (error) {
      setCalculationError(error);
      return;
    }

    const temp = JSON.parse(JSON.stringify(inputs.additionalColumns));
    temp[currentCalculationIndex].calculationFormula = calculationFormula;
    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );

    // Reset state and close modal
    setCalculationFormula("");
    setCalculationError("");
    setCurrentCalculationIndex(null);
    setIsCalculateModalVisible(false);
  };

  const handleOpenCalculationModal = (index) => {
    setCurrentCalculationIndex(index);
    setCalculationFormula(
      inputs.additionalColumns[index]?.calculationFormula || ""
    );
    setCalculationError("");
    setIsCalculateModalVisible(true);
  };

  const getAvailableColumns = () => {
    const columns = [];

    // Add result fields
    if (inputs?.resultFields) {
      inputs.resultFields.forEach((field) => {
        if (field.name) {
          columns.push({
            label: field.name,
            value: field.name,
          });
        }
      });
    }

    // Add additional columns
    if (inputs?.additionalColumns) {
      inputs.additionalColumns.forEach((column) => {
        if (column.title) {
          columns.push({
            label: column.title,
            value: column.title,
          });
        }
      });
    }

    return columns;
  };

  const footerTypeOptions = [
    { label: "Title", value: "title" },
    { label: "Calculation", value: "calculation" },
  ];

  const handleOpenFooterModal = () => {
    setFooterFields(inputs?.footerFields || {});
    setSelectedColumn("");
    setIsFooterModalVisible(true);
  };

  const handleColumnSelect = (e) => {
    setSelectedColumn(e.target.value);
    setCurrentFooterIndex(null);
    setFooterFormula("");
    setFooterError("");
  };

  const handleFooterTypeChange = (e, index) => {
    const newFooterFields = { ...footerFields };
    if (!newFooterFields[selectedColumn]) {
      newFooterFields[selectedColumn] = [];
    }
    newFooterFields[selectedColumn][index] = {
      ...newFooterFields[selectedColumn][index],
      type: e.target.value,
      value: "", // Clear the value when type changes
    };
    setFooterFields(newFooterFields);
  };

  const handleFooterValueChange = (e, index) => {
    const newFooterFields = { ...footerFields };
    if (!newFooterFields[selectedColumn]) {
      newFooterFields[selectedColumn] = [];
    }
    newFooterFields[selectedColumn][index] = {
      ...newFooterFields[selectedColumn][index],
      value: e.target.value,
    };
    setFooterFields(newFooterFields);
    setFooterError("");
  };

  const handleAddFooterField = () => {
    const newFooterFields = { ...footerFields };
    if (!newFooterFields[selectedColumn]) {
      newFooterFields[selectedColumn] = [];
    }
    newFooterFields[selectedColumn].push({ type: "title", value: "" });
    setFooterFields(newFooterFields);
  };

  const handleDeleteFooterField = (index) => {
    const newFooterFields = { ...footerFields };
    if (newFooterFields[selectedColumn]) {
      newFooterFields[selectedColumn] = newFooterFields[selectedColumn].filter(
        (_, i) => i !== index
      );
      if (newFooterFields[selectedColumn].length === 0) {
        delete newFooterFields[selectedColumn];
      }
      setFooterFields(newFooterFields);
    }
  };

  const validateFooterFormula = (formula) => {
    // Get all available numeric column names
    const availableColumns = [
      // Filter resultFields for numeric types
      ...(inputs?.resultFields
        ?.filter(
          (field) =>
            field.elementData?.type === "number" ||
            field.elementData?.type === "currency"
        )
        .map((field) => field.name) || []),

      // Filter additionalColumns for numeric types
      ...(inputs?.additionalColumns
        ?.filter(
          (column) =>
            column.dataType === "number" ||
            column.dataType === "currency" ||
            column.dataType === "percentage"
        )
        .map((column) => column.title) || []),
    ].filter(Boolean);

    // Check for balanced parentheses (excluding those in column names)
    let parenthesesCount = 0;
    let inColumnName = false;
    for (const char of formula) {
      if (char === "(" && !inColumnName) parenthesesCount++;
      if (char === ")" && !inColumnName) parenthesesCount--;
      if (char === " ") inColumnName = false;
      if (char === "(" && inColumnName) inColumnName = true;
      if (parenthesesCount < 0) {
        return "Unbalanced parentheses in formula.";
      }
    }
    if (parenthesesCount !== 0) {
      return "Unbalanced parentheses in formula.";
    }

    // Check for valid characters (allowing spaces, letters, numbers, operators, parentheses, %, [], and :)
    const validChars = /^[a-zA-Z0-9\+\-\*\/\(\)\s\[\]:%]+$/;
    if (!validChars.test(formula)) {
      // Find invalid characters for better error message
      const invalidChars = [
        ...new Set(formula.split("").filter((char) => !validChars.test(char))),
      ];
      return `Invalid characters in formula: ${invalidChars.join(
        ", "
      )}. Only letters, numbers, spaces, +, -, *, /, %, [], :, and parentheses are allowed.`;
    }

    // Check for incomplete expressions (operators at the end)
    const trimmedFormula = formula.trim();
    if (/[\+\-\*\/]$/.test(trimmedFormula)) {
      return "Formula cannot end with an operator.";
    }

    // Check for consecutive operators
    if (/[\+\-\*\/]\s*[\+\-\*\/]/.test(trimmedFormula)) {
      return "Formula cannot have consecutive operators.";
    }

    // Check for operators at the start (except minus for negative numbers)
    if (/^[\+\*\/]/.test(trimmedFormula)) {
      return "Formula cannot start with an operator (except minus for negative numbers).";
    }

    // Extract function name and arguments
    const functionMatch = formula.match(/^([A-Za-z]+)\((.*)\)$/);
    if (!functionMatch) {
      // If it's not a function, validate it as a regular calculation
      const parts = formula.split(/[\+\-\*\/]/).map((part) => part.trim());
      for (const part of parts) {
        if (part && !availableColumns.includes(part) && !/^\d+%?$/.test(part)) {
          return `Invalid column name or number: ${part}`;
        }
      }
      return "";
    }

    const [, functionName, args] = functionMatch;
    if (
      !["SUM", "AVG", "COUNT", "PRODUCT"].includes(functionName.toUpperCase())
    ) {
      return `Invalid function name: ${functionName}. Supported functions are SUM, AVG, COUNT, and PRODUCT.`;
    }

    // Split arguments by comma and validate each
    const columnRefs = args.split(",").map((arg) => arg.trim());
    for (const ref of columnRefs) {
      // Check for range syntax (e.g., ColumnName[1:5])
      const rangeMatch = ref.match(
        /^([A-Za-z0-9\s\-\(\)]+)\[(\d+|\d+:\d+|\d+:end|end:\d+)\]$/
      );
      if (rangeMatch) {
        const [, columnName, range] = rangeMatch;
        if (!availableColumns.includes(columnName)) {
          return `Invalid column name in range: ${columnName}`;
        }
        // Validate range format
        if (!/^\d+$|^\d+:\d+$|^\d+:end$|^end:\d+$/.test(range)) {
          return `Invalid range format: ${range}`;
        }
        // If it's a numeric range, ensure start is less than end
        if (range.includes(":")) {
          const [start, end] = range.split(":");
          if (
            start !== "end" &&
            end !== "end" &&
            parseInt(start) >= parseInt(end)
          ) {
            return `Invalid range: start value (${start}) must be less than end value (${end})`;
          }
        }
      } else {
        // Simple column reference
        if (!availableColumns.includes(ref)) {
          return `Invalid column name: ${ref}`;
        }
      }
    }

    return "";
  };

  const handleSaveFooterFields = () => {
    // Validate all calculations before saving
    const errors = Object.entries(footerFields).flatMap(([column, fields]) =>
      fields
        .map((field, index) => {
          if (field.type === "calculation" && field.value) {
            const error = validateFooterFormula(field.value);
            if (error) {
              return { column, index, error };
            }
          }
          return null;
        })
        .filter(Boolean)
    );

    if (errors.length > 0) {
      setFooterError(
        `Error in calculation for ${errors
          .map((e) => `"${footerFields[e.column][e.index].value}"`)
          .join(", ")}`
      );
      return;
    }

    handleInputChange(
      createTargetObject(`${inputs?.guid}.footerFields`, footerFields)
    );
    setIsFooterModalVisible(false);
  };

  const handleDeleteColumn = (index) => {
    const temp = JSON.parse(JSON.stringify(inputs.additionalColumns));
    const columnToDelete = temp[index];
    temp.splice(index, 1);

    // Remove footer fields that reference the deleted column
    const footerFields = JSON.parse(JSON.stringify(inputs.footerFields || {}));
    delete footerFields[columnToDelete.title];

    // Update additionalColumns
    handleInputChange(
      createTargetObject(`${inputs?.guid}.additionalColumns`, temp)
    );

    // Update footerFields
    handleInputChange(
      createTargetObject(`${inputs?.guid}.footerFields`, footerFields)
    );
  };

  const searchColumns = (event) => {
    const formula = event.query;
    const numericColumns = getNumericColumnNames(inputs);

    // Extract the current word being typed
    const words = formula.split(/[\+\-\*\/\(\)\s]/);
    const currentWord = words[words.length - 1].toLowerCase();

    // If no current word, show all numeric columns
    if (!currentWord) {
      setFilteredColumns(numericColumns);
      return;
    }

    // Filter columns that match the current word
    const filtered = numericColumns.filter((column) =>
      column.toLowerCase().includes(currentWord)
    );

    // If no matches found, show all numeric columns
    if (filtered.length === 0) {
      setFilteredColumns(numericColumns);
    } else {
      setFilteredColumns(filtered);
    }
  };

  const getCurrentWordAtPosition = (formula, position) => {
    // Find the start of the current word
    let start = position;
    while (start > 0 && !/[\+\-\*\/\(\)\s]/.test(formula[start - 1])) {
      start--;
    }

    // Find the end of the current word
    let end = position;
    while (end < formula.length && !/[\+\-\*\/\(\)\s]/.test(formula[end])) {
      end++;
    }

    return formula.slice(start, end);
  };

  const searchFooterColumns = (event) => {
    const formula = event.query;
    const numericColumns = getNumericColumnNames(inputs);

    // Extract the current word being typed
    const words = formula.split(/[\+\-\*\/\(\)\s\[\]:]/);
    const currentWord = words[words.length - 1].toLowerCase();

    // If no current word, show all numeric columns
    if (!currentWord) {
      setFilteredColumns(numericColumns);
      return;
    }

    // Filter columns that match the current word
    const filtered = numericColumns.filter((column) =>
      column.toLowerCase().includes(currentWord)
    );

    // If no matches found, show all numeric columns
    if (filtered.length === 0) {
      setFilteredColumns(numericColumns);
    } else {
      setFilteredColumns(filtered);
    }
  };

  return (
    <div className={SwimLaneStyles.scrollContainer}>
      <Modal
        header={"Make Columns Editable"}
        visible={isEditModalVisible}
        onHide={() => setIsEditModalVisible(false)}
        width={20}
      >
        <div style={{ padding: "20px" }}>
          {/* May implement this based on Guna's feedback.
          <div style={{ marginBottom: '20px' }}>
            <h3>Primary Search Field</h3>
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <Checkbox
                checked={inputs?.primarySearchField?.editable ?? false}
                onChange={(e) => handleEditableChange('primarySearchField', null, e.checked)}
              />
              <span>{inputs?.primarySearchField?.label}</span>
            </div>
          </div> */}

          <div style={{ marginBottom: "20px" }}>
            <h3>Result Fields</h3>
            {inputs?.resultFields?.map((field, index) => (
              <div
                key={field.id}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                  marginBottom: "10px",
                }}
              >
                <Checkbox
                  checked={field.editable ?? false}
                  onChange={(e) =>
                    handleEditableChange("resultFields", index, e.checked)
                  }
                />
                <span>{field.name}</span>
              </div>
            ))}
          </div>

          {/* May implement this based on Guna's feedback.
          <div>
            <h3>Additional Columns</h3>
            {inputs?.additionalColumns?.map((column, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
                <Checkbox
                  checked={column.editable ?? false}
                  onChange={(e) => handleEditableChange('additionalColumns', index, e.checked)}
                />
                <span>{column.title}</span>
              </div>
            ))}
          </div> */}
        </div>
      </Modal>
      <Modal
        header={"Add Calculations"}
        visible={isCalculateModalVisible}
        onHide={() => {
          setCalculationFormula("");
          setCalculationError("");
          setCurrentCalculationIndex(null);
          setIsCalculateModalVisible(false);
        }}
        width={50}
      >
        <div style={{ padding: "20px" }}>
          <div style={{ marginBottom: "20px" }}>
            <SettingsLabel label="Calculation Formula" />
            <AutoComplete
              value={calculationFormula}
              suggestions={filteredColumns}
              completeMethod={searchColumns}
              onChange={(e) => {
                const newValue = e.value;
                setCalculationFormula(newValue);
                setCalculationError("");

                // Get the current word at cursor position
                const word = getCurrentWordAtPosition(newValue, cursorPosition);
                setCurrentWord(word);
              }}
              onSelect={(e) => {
                const selectedColumn = e.value;
                const currentFormula = calculationFormula;
                const wordStart = currentFormula.lastIndexOf(
                  currentWord,
                  cursorPosition
                );
                const newFormula =
                  currentFormula.slice(0, wordStart) +
                  selectedColumn +
                  currentFormula.slice(wordStart + currentWord.length);
                setCalculationFormula(newFormula);
              }}
              onFocus={(e) => {
                setCursorPosition(e.target.selectionStart);
              }}
              onKeyUp={(e) => {
                setCursorPosition(e.target.selectionStart);
                const word = getCurrentWordAtPosition(
                  calculationFormula,
                  e.target.selectionStart
                );
                setCurrentWord(word);
              }}
              style={{ width: "100%" }}
              placeholder="e.g. Quantity + Price"
              delay={150}
              minLength={1}
              dropdown
            />
            <div
              style={{ fontSize: "0.8rem", color: "#666", marginTop: "5px" }}
            >
              Enter a mathematical formula using column names and operators (+,
              -, *, /). Use parentheses for grouping. Only numeric columns can
              be used in calculations.
            </div>
            {calculationError && (
              <div style={{ color: "red", marginTop: "10px" }}>
                {calculationError}
              </div>
            )}
          </div>
          <div
            style={{ display: "flex", justifyContent: "flex-end", gap: "10px" }}
          >
            <button
              onClick={() => {
                setCalculationFormula("");
                setCalculationError("");
                setCurrentCalculationIndex(null);
                setIsCalculateModalVisible(false);
              }}
              className="p-button p-button-text"
            >
              Cancel
            </button>
            <button onClick={handleSaveCalculation} className="p-button">
              Save
            </button>
          </div>
        </div>
      </Modal>
      <Modal
        header={"Footer Fields"}
        visible={isFooterModalVisible}
        onHide={() => {
          setFooterFields({});
          setFooterFormula("");
          setFooterError("");
          setCurrentFooterIndex(null);
          setSelectedColumn("");
          setIsFooterModalVisible(false);
        }}
        width={50}
      >
        <div style={{ padding: "20px" }}>
          <div style={{ marginBottom: "20px" }}>
            <SettingsLabel label="Select Column" />
            <SelectInput
              options={getAvailableColumns()}
              value={selectedColumn}
              onChange={handleColumnSelect}
              style={{ width: "100%" }}
            />
            <div
              style={{ fontSize: "0.8rem", color: "#666", marginTop: "5px" }}
            >
              Choose a column from the dropdown to add footer fields to it. Once
              selected, you can add multiple footer fields for that column. For
              each footer field, you can:
              <ul style={{ margin: "5px 0 0 20px", padding: 0 }}>
                <li>Set a type (title or calculation)</li>
                <li>
                  For titles: Enter text that will appear at the bottom of the
                  column
                </li>
                <li>
                  For calculations: Enter a formula using supported functions
                  (SUM, AVG, COUNT) and column references. Use [1:5] for ranges
                </li>
              </ul>
            </div>
          </div>

          {selectedColumn && (
            <>
              {(footerFields[selectedColumn] || []).map((field, index) => (
                <div
                  key={index}
                  style={{ marginBottom: "20px", position: "relative" }}
                >
                  <div style={{ marginBottom: "10px" }}>
                    <SettingsLabel
                      label={
                        field.type === "title"
                          ? "Footer Title"
                          : "Footer Calculation"
                      }
                    />
                    <div
                      style={{
                        display: "flex",
                        gap: "10px",
                        alignItems: "center",
                      }}
                    >
                      {field.type === "title" ? (
                        <InputText
                          value={field.value}
                          onChange={(e) => handleFooterValueChange(e, index)}
                          style={{ flex: 1 }}
                          placeholder="Enter footer title"
                        />
                      ) : (
                        <AutoComplete
                          value={field.value}
                          suggestions={filteredColumns}
                          completeMethod={searchFooterColumns}
                          onChange={(e) => handleFooterValueChange(e, index)}
                          onSelect={(e) => {
                            const selectedColumn = e.value;
                            const currentFormula = field.value;
                            const words = currentFormula.split(
                              /[\+\-\*\/\(\)\s\[\]:]/
                            );
                            const lastWord = words[words.length - 1];
                            const lastIndex =
                              currentFormula.lastIndexOf(lastWord);
                            const newFormula =
                              currentFormula.slice(0, lastIndex) +
                              selectedColumn;
                            handleFooterValueChange(
                              { target: { value: newFormula } },
                              index
                            );
                          }}
                          style={{ flex: 1 }}
                          placeholder="e.g. SUM(Quantity), AVG(Price[1:5])"
                          delay={150}
                          minLength={1}
                          dropdown
                        />
                      )}
                      <button
                        onClick={() => handleDeleteFooterField(index)}
                        className="p-button p-button-danger p-button-text"
                        style={{ padding: "0.5rem" }}
                      >
                        <i
                          className="pi pi-trash"
                          style={{ fontSize: "1.2rem" }}
                        ></i>
                      </button>
                    </div>
                  </div>
                  <div style={{ marginBottom: "10px" }}>
                    <SettingsLabel label="Type" />
                    <SelectInput
                      options={footerTypeOptions}
                      value={field.type || "title"}
                      onChange={(e) => handleFooterTypeChange(e, index)}
                      style={{ width: "100%" }}
                    />
                  </div>
                </div>
              ))}
              {footerError && (
                <div style={{ color: "red", marginBottom: "10px" }}>
                  {footerError}
                </div>
              )}
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  marginTop: "20px",
                }}
              >
                <button
                  onClick={handleAddFooterField}
                  className="p-button p-button-text"
                >
                  + Add Footer Field
                </button>
                <button onClick={handleSaveFooterFields} className="p-button">
                  Save All
                </button>
              </div>
            </>
          )}
        </div>
      </Modal>
      <SettingsMenu
        inputs={inputs}
        handleInputChange={handleInputChange}
        hideMenu={hideDialog}
      />
      <div className={SwimLaneStyles.settingsTab}>
        <div>Advanced</div>
      </div>
      <div className={SwimLaneStyles.settingsMenuOptionsContainer}>
        <div>
          <SettingsLabel label="Subtitle" />
          <LexicalEditor
            name={`${inputs?.guid}.subtitle`}
            value={inputs?.subtitle ?? false}
            onChange={assignValuesNested}
          />
        </div>
        <div>
          <ColumnSizeDropdowm
            name={`${inputs?.guid}.divClassName`}
            inputs={inputs}
            onChange={handleInputChange}
          />
        </div>
        <div>
          <SelectModule inputs={inputs} handleInputChange={handleInputChange} />
        </div>
        <div>
          <SourceForm
            inputs={inputs}
            sourceFormOptionsLabels={sourceFormOptionsLabels}
            handleInputChange={handleInputChange}
            sourceFormOptions={sourceFormOptions}
            solutionId={solutionId}
            callApi={callApi}
          />
        </div>
        <div>
          <PrimarySearchField
            inputs={inputs}
            handleInputChange={handleInputChange}
            isObjectLinkGrid={true}
            loading={loading}
            solutionId={solutionId}
          />
        </div>
        <div>
          <SettingsLabel label="Result Fields" />
          <AsyncResultFieldsDropdown
            onSelect={handleValueChange}
            disabled={!inputs?.primarySearchField}
            selectedOptions={inputs?.resultFields}
            options={resultFieldOptions}
            onFocus={handleFocus}
            showOptions={showOptions}
            onOutsideClick={handleOutsideClick}
            handleColumnChange={handleColumnChange}
            isObjectLinkGrid={true}
          />
        </div>
        <div>
          {inputs.additionalColumns.length > 0 && (
            <>
              <SettingsLabel label="Additional Columns" />
              {inputs.additionalColumns.map((column, index) => {
                return (
                  <div
                    key={index}
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      gap: "10px",
                      marginTop: "10px",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "5px",
                      }}
                    >
                      <div style={{ display: "flex" }}>
                        <SettingsLabel label={`Title (${index + 1})`} />
                        <button
                          onClick={() => handleDeleteColumn(index)}
                          className="p-button p-button-danger p-button-text"
                          style={{ padding: "0 0 0.5rem 0.5rem" }}
                        >
                          <i
                            className="pi pi-trash"
                            style={{ fontSize: "1.2rem" }}
                          ></i>
                        </button>
                      </div>
                      <InputText
                        value={column.title}
                        onChange={(e) => handleColumnTitleChange(e, index)}
                      />
                    </div>
                    <SelectInput
                      label={`Data Type (${index + 1})`}
                      options={dataTypeOptions}
                      value={column.dataType}
                      onChange={(e) => handleColumnDataTypeChange(e, index)}
                    />
                    {(column.dataType === "dropdown" ||
                      column.dataType === "multiselect") && (
                      <div>
                        <div>
                          {column?.dropdownOptions &&
                            column.dropdownOptions.map(
                              (option, dropdownIndex) => {
                                return (
                                  <InputText
                                    key={dropdownIndex}
                                    value={option.label}
                                    onChange={(e) =>
                                      handleDropdownOptionChange(
                                        e,
                                        index,
                                        dropdownIndex
                                      )
                                    }
                                    style={{ marginBottom: "10px" }}
                                  />
                                );
                              }
                            )}
                        </div>
                        <AddOptionButton
                          onClick={() => addNewDropdownOption(index)}
                          label="+ Add New Option"
                        />
                      </div>
                    )}
                    {isNumericDataType(column.dataType) && (
                      <div>
                        <Toggle
                          checked={column?.hasCalculate}
                          onChange={(e) => handleCalculateChange(e, index)}
                          label={"Calculation"}
                        />
                      </div>
                    )}
                    {column.hasCalculate && (
                      <div>
                        <AddOptionButton
                          label="Add Calculations"
                          onClick={() => handleOpenCalculationModal(index)}
                        />
                        {column.calculationFormula && (
                          <div style={{ marginTop: "10px", color: "#666" }}>
                            Formula: {column.calculationFormula}
                          </div>
                        )}
                      </div>
                    )}
                    <div
                      style={{
                        display: "flex",
                        gap: "5px",
                        marginTop: `${
                          column.dataType === "dropdown" ||
                          column.dataType === "multiselect"
                            ? "0"
                            : "10px"
                        }`,
                      }}
                    >
                      <div
                        style={{
                          display: "flex",
                          gap: "5px",
                          alignItems: "center",
                        }}
                      >
                        <Checkbox
                          checked={column.isRequired}
                          onChange={(e) => handleColumnRequiredChange(e, index)}
                        />
                        <SettingsLabel label={"Required"} />
                      </div>
                    </div>
                  </div>
                );
              })}
            </>
          )}
        </div>
        <div>
          <AddOptionButton label="+ Add New Column" onClick={addNewColumn} />
        </div>
        <div>
          <AddOptionButton
            label="Make Columns Editable"
            onClick={() => setIsEditModalVisible(true)}
          />
        </div>
        <div>
          <AddOptionButton
            label="Configure Footer Fields"
            onClick={handleOpenFooterModal}
          />
        </div>
      </div>
    </div>
  );
}
