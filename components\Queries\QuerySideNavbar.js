import React from "react"
import { Dropdown } from "primereact/dropdown"
import { useQueriesPageContext } from "../../public/ContextProviders/QueriesPageContextProvider"
import { Label } from '../UI/Label/Label'
import { FormsWorkflowModule } from "./QuerySideNavbarModules/FormsWorkflowModule"
import { PapyrusDriveModule } from "./QuerySideNavbarModules/PapyrusDriveModule"

import styles from "./QueryDashboard.module.css"

export const QuerySideNavbar = () => {
  const { formDefinitionMasterFilter, setFormDefinitionMasterFilter, setQueryResult, setDidUserDecideOnConditionals } = useQueriesPageContext()

  const handleModuleChange = (e) => {
    setFormDefinitionMasterFilter(prevState => ({
      ...prevState,
      module: e.value,
    }))

    // This is to clear the value in the Results counter above the dashboard. - Alex
    setQuer<PERSON><PERSON>ult([])
  }

  const valueTemplate = (option, props) => {
    if (option) {
      return (<div className="flex align-items-center">
        <div style={{ textTransform: "none" }}>{option.label}</div></div>);
    } return <span>{props.placeholder}</span>;
  };
  const itemTemplate = (option) => {
    return (<div className="flex align-items-center">
      <div style={{ textTransform: "none" }}>{option.label}</div></div>);
  };
  return (
    <>
      <InputContainer containerStyles={{ width: '100%' }}>
        <Label label="Module" asterisk />
        <Dropdown
          value={formDefinitionMasterFilter.module}
          options={[
            { label: "innovForms", value: 0 },
            { label: "innovDocs", value: 1, }
          ]}
          itemTemplate={itemTemplate}
          valueTemplate={valueTemplate}
          onChange={handleModuleChange}
          style={{ width: '100%' }}
          optionValue="value"
        />
        <span style={{ fontSize: "14px" }}>
          Choose a module to proceed.
        </span>
      </InputContainer>
      <FormsWorkflowModule />
      <PapyrusDriveModule />
    </>
  )
}

export const InputContainer = ({ children, containerStyles }) => (
  <div className={styles.inputContainer} style={containerStyles}>
    {children}
  </div>
) 