import styles from './Menu.module.css'
import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import mini from '../../../../svg/Main Dashboard/Minimize.svg'
import expand from '../../../../svg/Main Dashboard/Minimize_right.svg'
import LMSLogo from '../../../../public/svg/LMS/LMS Logo.svg'
import toggle from '../../../../public/svg/LMS/Toggle.svg'
import { ConditionalDisplay } from '../../ConditionalDisplay/ConditionalDisplay'
import ITSMQuestionMarkIcon from '../../../../images/itsm_question_mark_icon.png'
import clsx from 'clsx'

let instance = null

export const Menu = ({ activeSection, isCollapsed, toggleCollapse, children, pin, setPin, metronic }) => {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth)

  const handleToggle = () => {
    setPin((prev) => !prev)
  }

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <aside
      className={isCollapsed ? `${styles.menu} ${styles.collapsed}` : styles.menu}
      onMouseEnter={() => {
        if (instance) clearTimeout(instance)
        if (!pin) toggleCollapse(false)
      }}
      onMouseLeave={() => {
        if (instance) clearTimeout(instance)
        instance = setTimeout(() => {
          if (!pin) toggleCollapse(true)
        }, 4000)
      }}
    >
      <div
        className={
          isCollapsed
            ? `${metronic ? styles.metronicContainer : styles.container} ${styles.collapsedContainer}`
            : metronic
            ? styles.metronicContainer
            : styles.container
        }
        style={{
          borderRight: isCollapsed && metronic ? '3px solid #5151511a' : '1px solid #5151511a'
        }}
      >
        {windowWidth > 900 ? (
          <>
            <div className={clsx('mb-5', styles.toggleIcon)}>
              {/* {metronic && (
                <Image src={LMSLogo} alt="LOGO" width={73} height={34} />
              )} */}
              <Image
                src={!metronic ? (pin ? expand : mini) : toggle}
                alt="Minimize"
                onClick={handleToggle}
                style={{
                  // marginTop: '20px',
                  cursor: 'pointer',
                  position: metronic ? 'absolute' : 'relative',
                  right: metronic ? '-20px' : '0'
                }}
              ></Image>
            </div>
          </>
        ) : null}
        <ul className={styles.subMenu}>{children}</ul>
        <ConditionalDisplay condition={activeSection === 'ITSM' && !isCollapsed}>
          <footer className={styles.menuFooterITSM}>
            <Image src={ITSMQuestionMarkIcon} alt="ITSM Question Mark Icon" />
            <h3>Need Help?</h3>
            <p>Check out our help guides or submit a ticket</p>
            <button className={styles.ITSMButtonOutline}>Help Guides</button>
          </footer>
        </ConditionalDisplay>
        <ConditionalDisplay condition={activeSection !== 'ITSM'}>
          <footer className={styles.menuFooter}>
            <p>© 2025 {process.env.NEXT_PUBLIC_COMPANY_NAME}</p>
          </footer>
        </ConditionalDisplay>
      </div>
    </aside>
  )
}
