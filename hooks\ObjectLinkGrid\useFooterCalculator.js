import { useCallback } from 'react'

export const useFooterCalculator = (metadata, value, dataTableColumns) => {
  const calculateFunctionFormula = useCallback((functionMatch, value, dataTableColumns) => {
    const [, functionName, args] = functionMatch

    // Handle range syntax [start:end]
    const rangeMatch = args.match(/\[(\d+|end):(\d+|end)\]$/)
    let columnName = args
    let range = null

    if (rangeMatch) {
      columnName = args.replace(/\[.*\]/, '').trim()
      const start = rangeMatch[1] === 'end' ? value?.length : parseInt(rangeMatch[1])
      const end = rangeMatch[2] === 'end' ? value?.length : parseInt(rangeMatch[2])
      
      // Validate range bounds
      if (start < 1 || end < 1 || start > (value?.length || 0) || end > (value?.length || 0)) {
        console.warn(`Range [${start}:${end}] is out of bounds for data length ${value?.length || 0}`)
      }
      
      range = { start, end }
    }

    // Get all values from the rows
    const values = []

    if (value && value.length > 0) {
      value.forEach((rowData, rowIndex) => {
        const matchingColumn = dataTableColumns.find((c) => c.header === columnName)
        if (!matchingColumn) return

        const matchingKeys = Object.keys(rowData).filter((key) => {
          if (matchingColumn.field.startsWith(`${columnName}_`)) {
            return key.startsWith(columnName + '_')
          }
          return key === matchingColumn.field
        })

        matchingKeys.forEach((key) => {
          const val = rowData[key]
          if (val !== undefined && val !== null) {
            if (range) {
              if (rowIndex >= range.start - 1 && rowIndex <= range.end - 1) {
                const num = parseFloat(val)
                if (!isNaN(num)) values.push(num)
              }
            } else {
              const num = parseFloat(val)
              if (!isNaN(num)) values.push(num)
            }
          }
        })
      })
    }

    let result = 0
    switch (functionName.toUpperCase()) {
      case 'SUM':
        result = values.reduce((a, b) => a + b, 0)
        break
      case 'AVG':
        result = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0
        break
      case 'COUNT':
        result = values.length
        break
      case 'PRODUCT':
        result = values.length > 0 ? values.reduce((a, b) => a * b, 1) : 0
        break
      default:
        console.error(`Unsupported function: ${functionName}`)
        result = 0
        break
    }

    return result
  }, [value, dataTableColumns])

  const calculateArithmeticFormula = useCallback((formula, value, dataTableColumns) => {
    try {
      const columnTotals = {}
      
      // Get all numeric columns mentioned in the formula
      const columnNames = formula.split(/[\+\-\*\/\(\)\s]/)
        .map(part => part.trim())
        .filter(part => part && !/^\d+$/.test(part))
      
      // For each column name, calculate the total across all rows
      columnNames.forEach(columnName => {
        const matchingColumn = dataTableColumns.find(c => c.header === columnName)
        
        if (matchingColumn) {
          let total = 0
          if (value && value.length > 0) {
            value.forEach(rowData => {
              const val = rowData[matchingColumn.field]
              if (val !== undefined && val !== null) {
                const num = parseFloat(val)
                if (!isNaN(num)) {
                  total += num
                }
              }
            })
          }
          columnTotals[columnName] = total
        }
      })
      
      // Replace column names with their totals in the formula
      let evaluatedFormula = formula
      Object.keys(columnTotals).forEach(columnName => {
        const regex = new RegExp(columnName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
        evaluatedFormula = evaluatedFormula.replace(regex, columnTotals[columnName])
      })
      
      const result = new Function('return ' + evaluatedFormula)()
      return result
    } catch (error) {
      console.error('Error evaluating arithmetic formula:', error)
      return `Error: ${error.message}`
    }
  }, [value, dataTableColumns])

  const calculateFooterValues = useCallback(() => {
    // If value is masked, don't return any footer values
    if (value === '●●●●●●●●') return []

    if (!metadata?.footerFields) return []

    // Get all unique column names that have footer fields
    const columnNames = Object.keys(metadata.footerFields)
    if (columnNames.length === 0) return []

    // Find the maximum number of footer rows needed
    const maxFooterRows = Math.max(...columnNames.map((col) => metadata.footerFields[col].length))

    // Create footer rows
    const footerRows = Array(maxFooterRows)
      .fill({})
      .map((_, rowIndex) => {
        const row = {}
        columnNames.forEach((col) => {
          const footerField = metadata.footerFields[col][rowIndex]
          
          if (footerField) {
            // Find the corresponding column in dataTableColumns
            const column = dataTableColumns.find((c) => c.header === col)
            if (!column) return

            if (footerField.type === 'calculation') {
              // For calculations, we need to evaluate the formula
              const formula = footerField.value

              const functionMatch = formula.match(/^([A-Za-z]+)\((.*)\)$/)
              if (functionMatch) {
                const result = calculateFunctionFormula(functionMatch, value, dataTableColumns)
                row[column.field] = result
              } else {
                const result = calculateArithmeticFormula(formula, value, dataTableColumns)
                row[column.field] = result
              }
            } else {
              // For titles, just use the value directly
              row[column.field] = footerField.value
            }
          }
        })

        return row
      })

    return footerRows
  }, [metadata?.footerFields, value, dataTableColumns, calculateFunctionFormula, calculateArithmeticFormula])

  return {
    calculateFooterValues
  }
}
