import { SolutionIds } from "../src/contants";
import { useApi } from "./useApi";

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API;

const sourceTable = [
  {
    id: 1,
    label: "Item List",
    value: "ItemList",
    data: [],
    url: "PurchaseOrderRequistion/Item?page=-1",
  },
];

// TODO: Rename this to hook to a more generic name.
export const useFetchSourceForm = () => {
  const { callApi ,loading } = useApi();

  const fetchFormDefinition = async ({
    acquireToken,
    setSourceFormOptions,
    setSourceFormOptionsLabels,
    inputs,
    handleInputChange,
    isSpreadsheet,
    solutionId,
  }) => {
    try {
      // TODO: Either update this endpoint or use a different API so that we can grab all form definitions that have object linking enabled and are published.
      // Adding Rows=50 is a temporary solution to get the form definitions that have object linking enabled and are published.
      const formDefinitionUrl = `FormDefinition/filter?Status=1&&HasObjectLinking=true&HasFormEverBeenPublished=true&Rows=50`;

      const formDefinitionParams = {
        method: "GET",
        url: formDefinitionUrl,
      };

      const result = await callApi(formDefinitionParams);

      if (!result) {
        setSourceFormOptions([]);
        return;
      }

      if (isSpreadsheet) {
        const sourceTableLabels = sourceTable?.map((item) => item.label);
        setSourceFormOptionsLabels(sourceTableLabels);
        return setSourceFormOptions(sourceTable);
      }

      const formDefinitionOptions = result?.data?.formDefinitions?.map(
        (form) => ({
          label: form.name,
          data: mapObjectToArray(form.metadata.metadata.form),
          id: form.id, // TODO: Check if this is needed and delete if not.
        })
      );

      const filteredFormDefinitionOptions = formDefinitionOptions.map(
        (option) => ({
          ...option,
          data: filterObjectsByType(option.data),
        })
      );

      // If we have inputs and handleInputChange, update the form data
      if (inputs && handleInputChange) {
        // Find the selected form data
        const selectedForm = filteredFormDefinitionOptions.find(
          (form) => form.label === inputs.sourceFormName
        );

        if (selectedForm) {
          // Update sourceFormData with the new form data
          handleInputChange({
            target: {
              name: `${inputs.guid}.sourceFormData`,
              value: {
                id: selectedForm.id,
                data: selectedForm.data,
              },
            },
          });

          // Update resultFields with the new form data
          handleInputChange({
            target: {
              name: `${inputs.guid}.resultFields`,
              value: selectedForm.data,
            },
          });

          // Only update primarySearchField if it doesn't exist in the new form data
          if (inputs.primarySearchField) {
            const primaryFieldExists = selectedForm.data.some(
              (field) => field.guid === inputs.primarySearchField.guid
            );

            if (!primaryFieldExists) {
              handleInputChange({
                target: {
                  name: `${inputs.guid}.primarySearchField`,
                  value: null,
                },
              });
            }
          }
        }
      }

      console.log(
        "filteredFormDefinitionOptions",
        filteredFormDefinitionOptions
      );

      const formDefinitionOptionsLabels = filteredFormDefinitionOptions.map(
        (option) => option.label
      );

      setSourceFormOptions(filteredFormDefinitionOptions);
      setSourceFormOptionsLabels(formDefinitionOptionsLabels);
    } catch (error) {
      console.error("Error fetching form definition master:", error);
      setSourceFormOptions([]);
    }
  };

  function mapObjectToArray(obj) {
    const result = [];

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const item = obj[key];

        if (item.guid) {
          result.push(item);
        }
      }
    }

    return result;
  }

  function filterObjectsByGuid(guid, objArray) {
    console.log("onfilter", guid, objArray);
    if (!guid || !objArray) return [];
    return objArray.filter((item) => item.guid !== guid);
  }

  function filterObjectsByType(objArray) {
    if (!objArray) return [];
    return objArray.filter((item) => validTypes.includes(item.type));
  }

  const validTypes = [
    "text",
    "textarea",
    "number",
    "mask",
    "calendar",
    "time",
    "dropdown",
    "multiselect",
    "radiobutton",
    "checkbox",
  ];

  const transformResultFields = (fields,isSpreadsheet = false) => {
    // Create array of letters for all possible indices
    const letters = Array.from(
      { length: fields.length },
      (_, i) => String.fromCharCode(66 + i) // 65 is ASCII for 'A'
    );

    // Transform each field and add column value
    const transformedFields = fields.map((field, index) => ( {
      elementData: field,
      columnValue: letters[index],
      name: field.label,
      id: field.guid, // Use guid instead of name to ensure unique IDs
      title: field.label,
      // dataType: "text",
      isRequired: false,
      isEditable: false,
      // isVisible: true,
      // isReadOnly: false,
      // isDisabled: false,
    }));

    // Add columnList to each object
    return transformedFields.map((field) => ({
      ...field,
      columnList: letters,
    }));
  };

  return { fetchFormDefinition, filterObjectsByGuid, transformResultFields, loading };
};
