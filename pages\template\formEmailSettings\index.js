import { useState, useEffect } from 'react'
import { EmailTemplateEditor } from '../../../components/UI/EmailTemplate/EmailTemplateEditor'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import { PageElementContainer } from '../../../components/UI/Page/PageElementContainer/PageElementContainer'
import SelectInput from '../../../components/UI/Input/SelectInput/SelectInput'
import { getAccessTokenForScopeSilent } from '../../../src/GetAccessTokenForScopeSilent'
import { formBuilderApi } from '../../../api/ApiQueries'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import { useMsal } from '@azure/msal-react'
import Button from '../../../components/UI/Button/Button'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export default function Index() {
  const { accounts } = useMsal()
  const account = accounts[0] ?? {}
  const [status, setStatus] = useState('submitEmailId')
  const [loading, setLoading] = useState(false)
  const [emailTemplates, setEmailTemplates] = useState({
    submitEmailId: null,
    approvedEmailId: null,
    rejectedEmailId: null,
    cancelledEmailId: null,
    onHoldEmailId: null,
    demoteEmailId: null,
    restartEmailId: null,
    resumeEmailId: null,
    reviseEmailId: null,
    approvalRequestEmailId: null
  })

  console.log('emailTemplates:', emailTemplates)

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)

        const accessToken = await getAccessTokenForScopeSilent(formBuilderApi)

        const response = await fetch(`${api}FormEmailTemplates`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        setEmailTemplates(data)
      } catch (error) {
        console.error('Error fetching email templates:', error)
        setEmailTemplates([])
      } finally {
        setLoading(false)
      }
    }

    if (account) {
      loadData()
    }
  }, [])

  const [saveLoading, setSaveLoading] = useState(false)
  const handleSave = async () => {
    try {
      setSaveLoading(true)
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)

      const response = await fetch(`${api}FormEmailTemplates`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`
        },
        body: JSON.stringify({ ...emailTemplates })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('FormEmailTemplate updated successfully:', data)
    } catch (error) {
      console.error('Error saving form email settings:', error)
    } finally {
      setSaveLoading(false)
    }
  }

  const handleTemplateSelect = (templateId) => {
    setEmailTemplates((prevTemplates) => ({
      ...prevTemplates,
      [status]: templateId
    }))
  }

  console.log('emailTemplates status:', emailTemplates[status])

  return (
    <PageContainer>
      <div className="flex justify-content-between align-items-center" style={{ padding: '24px 0 0 0' }}>
        <BreadCrumbs
          title="Form Email Settings"
          breadcrumbItems={[
            { label: 'Home', url: '/' },
            { label: 'Form Email Settings', url: '/' }
          ]}
          theme="metronic"
        />
        <Button
          label="Save"
          theme="metronic"
          style={{ minWidth: 100, fontWeight: 600 }}
          onClick={() => handleSave()}
          loading={saveLoading}
          disabled={saveLoading}
        />
      </div>
      <PageElementContainer>
        <SelectInput
          label="Select Status"
          theme="metronic"
          required
          options={[
            { label: 'Submit', value: 'submitEmailId' },
            { label: 'Approved', value: 'approvedEmailId' },
            { label: 'Rejected', value: 'rejectedEmailId' },
            { label: 'Cancelled', value: 'cancelledEmailId' },
            { label: 'On Hold', value: 'onHoldEmailId' },
            { label: 'Demote', value: 'demoteEmailId' },
            { label: 'Restart', value: 'restartEmailId' },
            { label: 'Resume', value: 'resumeEmailId' },
            { label: 'Revise', value: 'reviseEmailId' },
            { label: 'Approval Request', value: 'approvalRequestEmailId' }
          ]}
          value={status}
          onChange={(e) => setStatus(e.value)}
        />
        <EmailTemplateEditor
          selectedTemplateId={emailTemplates[status]}
          onTemplateSelect={(id) => handleTemplateSelect(id)}
          emailVariables={[
            {
              title: 'Form Information',
              values: ['FormName', 'FormId', 'FormSubmissionId', 'FormSubmissionDate', 'FormSubmissionLink']
            },
            {
              title: 'Approver Information',
              values: ['ApproverEmail', 'ApproverName', 'ApproverId', 'ApproverRole', 'ApproverDepartmentName', 'ApproverJobTitle']
            },
            {
              title: 'Form Submitter Information',
              values: ['FormSubmitterEmail', 'FormSubmitterName']
            }
          ]}
        />
      </PageElementContainer>
    </PageContainer>
  )
}
