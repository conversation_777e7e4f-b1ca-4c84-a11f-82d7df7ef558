// import React from "react";
// import { Dialog } from "primereact/dialog";
// import CustomDialog from "../CustomDialog/CustomDialog";
// import { Tooltip } from "primereact/tooltip";

// function Modal({ header, visible, onHide, color, children, ...props }) {
//   return (
//     <CustomDialog
//       header={header}
//       visible={visible}
//       color={color}
//       onHide={onHide}
//       // style={{ width: `${width}vw` }}
//       breakpoints={{ "960px": "75vw", "641px": "100vw" }}
//       {...props}
//     >
//       {children}
//     </CustomDialog>
//   );
// }

// export default Modal;

import React from "react";
import { Dialog } from "primereact/dialog";
import CustomDialog from "../CustomDialog/CustomDialog";
import { Tooltip } from "primereact/tooltip";
import close from "../../../svg/blue_close.svg";
import closeIcon from "../../../svg/metronic/close.svg";
import Image from "next/image";
function Modal({
  header,
  visible,
  onHide,
  color,
  children,
  theme = "default",
  backgroundColor,
  ...props
}) {
  const tooltipTarget = React.createRef();
  const imageSrc = theme === "metronic" ? closeIcon : close;
  return (
    <Dialog
      className={`mx-2 md:mx-0 ${theme === "metronic" ? "custom-lead" : ""}`}
      header={header}
      visible={visible}
      onHide={onHide}
      color={color}
      draggable={false}
      style={{ width: `${props.width}vw`, backgroundColor: backgroundColor }}
      breakpoints={{ "960px": "75vw", "641px": "100vw" }}
      closeIcon={<Image ref={tooltipTarget} src={imageSrc} />}
      {...props}
    >
      <Tooltip target={tooltipTarget} content="Close"></Tooltip>
      {children}
    </Dialog>
  );
}

export default Modal;
