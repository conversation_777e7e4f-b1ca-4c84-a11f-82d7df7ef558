import { AuthenticatedTemplate } from "@azure/msal-react"
import { useRouter } from 'next/router'
import { useState } from "react"

import OnBoardingSubmissionViewPage from "../../../../../../components/OnBoarding/OnBoardingSubmissionViewPage/OnBoardingSubmissionViewPage"
import RoleBasedPage from "../../../../../../public/UserProfileContext/RoleBasedPage"
import OnBoardingFormApproval from '../../../../../../components/OnBoarding/OnBoardingFormApproval'
import OnBoardingContextProvider from '../../../../../../public/ContextProviders/OnBoardingContextProvider'
import OnboardingAuditHistoryDashboard from "../../../../../../components/UI/Dashboards/OnBoardingPageDashboard/OnboardingAuditHistoryDashboard/OnboardingAuditHistoryDashboard"
import { LineItemsDashboard } from "../../../../../../components/UI/Dashboards/OnBoardingPageDashboard/LineItemsDashboard/LineItemsDashboard"
import { ApprovalOptions } from "../../../../../../components/UI/ApprovalOptions/ApprovalOptions"
import { OverviewStatus } from '../../../../../../components/WorkflowBuilder/WorkflowNode/ApproverStatus/OverviewStatus'
import { PageContainer } from '../../../../../../components/UI/Page/PageContainer/PageContainer'
import { getFormDefinition, getFormDefinitionWithConfig, getFormSubmission } from "../../../../../../api/apiCalls"
import { useInputs } from '../../../../../../hooks/useInput'
import { useSubmitFormValidation } from '../../../../../../hooks/useSubmitFormValidation'
import { usePreventSubmit } from '../../../../../../hooks/usePreventSubmit'
import { useGetFormPermissions } from "../../../../../../hooks/useGetFormPermissions"

import { useFetchFormApprovalData } from '../../../../../../hooks/FormApproval/useFetchFormApprovalData'
import { useFormApprovalActions } from '../../../../../../hooks/FormApproval/useFormApprovalActions'
import { useSaveFormApprovalChanges } from '../../../../../../hooks/FormApproval/useSaveFormApprovalChanges'
import { useGetFormApprovalRevision } from '../../../../../../hooks/FormApproval/useGetFormApprovalRevision'
import { useControlRequestOVerview } from '../../../../../../hooks/OnBoarding/useControlRequestOverview'
import { ConditionalDisplay } from '../../../../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { SubmissionStages } from '../../../../../../src/contants'
import { OnboardingHeaderDetails } from '../../../../../../components/OnBoarding/OnboardingHeaderDetails/OnboardingHeaderDetails'
import { useFetchOnboardingData } from '../../../../../../hooks/OnBoarding/useFetchOnboardingData'
import { useOverviewData } from "../../../../../../hooks/OnBoarding/useOverviewData"
import { getAccessToken } from "../../../../../../utillites/getAccessToken"
import UnauthorizedPage from "../../../../../401"
import { ProgressSpinner } from 'primereact/progressspinner'

export default function FormApprovalPage({ fid, lineItemId, metadata, status, initialFormSubmission, initialFormSubmissionData, initialConditions }) {
    const router = useRouter()
    const solutionId = 3
    const parentSubmissionId = initialFormSubmission?.parentEmployeeOnboardingSubmission?.id

    const {
        currentFormSubmissionId,
        currentFormSubmission,
        currentFormSubmissionData,
        currentFormMetadata,
        currentFormConditions,
        isLoadingLineItem,
        handleFormApprovalPageChange,
        revertToInitialState,
        setCurrentFormSubmissionData
    } = useControlRequestOVerview({ fid, lineItemId, initialFormSubmission, initialFormSubmissionData, initialConditions, metadata, parentSubmissionId })
    console.log("initialFormSubmissionData:", initialFormSubmissionData)
    const { queryResult: onboardingChildrenData, allWorkflowEmails } = useFetchOnboardingData(parentSubmissionId)

    const { fidArray, globalOverviewState, addToGlobalOverviewState } = useOverviewData({ parentFormId: fid, onboardingChildrenData, headerMetadata: metadata, headerInputs: initialFormSubmissionData })
    
    // TODO: Update the components within the two ConditionalDisplays so they are more modular.
    // TODO: Create an effective way to update some of the dynamic data when changed in child components.

    // Create an array with a length of childFormSubmissions.length + 1, where each element corresponds to its index.
    // This is useful for pagination or tracking the total number of pages based on the submissions.
    const totalPagesArray = Array.from({ length: onboardingChildrenData?.childFormSubmissions.length + 1 }, (_, index) => index)

    const { formSubmissionMaster, refetchFormSubmissionMaster } = useFetchFormApprovalData({ fid, formSubmissionGuid: router.query.fid })

    // Move these down the dependency tree.
    const { inputs, setInputs, files, setFiles } = useInputs({ initialValues: currentFormSubmissionData })
    const { checkSamePersonForSignature } = usePreventSubmit({ metadata, inputs, setInputs, files, setFiles })
    const { checkErrors } = useSubmitFormValidation({ metadata, inputs, setInputs, setFiles })
    const { currentApproverFormPermissions } = useGetFormPermissions({ formSubmissionId: fid })
    const { setSaveChangesDialog } = useSaveFormApprovalChanges({ currentApproverFormPermissions, finalizedForm: currentFormSubmission?.isFinalized })
    const { setSelectedRevision, isActiveRevision } = useGetFormApprovalRevision({ formSubmissionMaster, currentFormSubmission, setCurrentFormSubmissionData, setInputs })
    const { loading, submitLoading, updateMultipleFormSubmissions, updateFormSubmission, deleteFormSubmission, formApprovalToast } = useFormApprovalActions({
        fid, currentFormSubmission, files, checkErrors, checkSamePersonForSignature, refetchFormSubmissionMaster, setSaveChangesDialog,
        setSelectedRevision, solutionId
    })

    const breadcrumbItems = [
        { label: "My Requests" },
    ]

    const [currentPage, setCurrentPage] = useState(0)

    // Show loading spinner while line item data is being fetched
    if (isLoadingLineItem) {
        return (
            <AuthenticatedTemplate>
                <OnBoardingContextProvider parentSubmissionId={parentSubmissionId}>
                    <PageContainer>
                        <div style={{ 
                            display: 'flex', 
                            justifyContent: 'center', 
                            alignItems: 'center', 
                            height: '50vh' 
                        }}>
                            <ProgressSpinner 
                                style={{ width: '150px', height: '150px', color: '#00b9ff' }} 
                                strokeWidth="3" 
                                animationDuration=".5s" 
                            />
                        </div>
                    </PageContainer>
                </OnBoardingContextProvider>
            </AuthenticatedTemplate>
        )
    }

    if (!(fid)) {
        return (
            <UnauthorizedPage />
        )
    }

    return (
        <AuthenticatedTemplate>
            <OnBoardingContextProvider parentSubmissionId={parentSubmissionId}>
                <PageContainer>
                    <ConditionalDisplay condition={initialFormSubmission?.status === SubmissionStages.Draft || currentFormSubmission?.status === SubmissionStages.Draft}>
                        {totalPagesArray.map((_, index) => (
                            <ConditionalDisplay condition={index === currentPage} key={fidArray[index]}>
                                <OnBoardingSubmissionViewPage
                                    toast={formApprovalToast}
                                    loading={submitLoading}
                                    updateMultipleFormSubmissions={updateMultipleFormSubmissions}
                                    // deleteMultipleFormSubmissions={deleteMultipleFormSubmissions} <- May implement this later as an enhancement.
                                    // updateFormSubmission={updateFormSubmission}
                                    deleteFormSubmission={deleteFormSubmission}
                                    currentFormSubmissionId={currentFormSubmissionId}
                                    // Will move these down into OnBoardingSubmissionViewPage
                                    conditions={"In Progress"}
                                    conditionMapper={"In Progress"}
                                    validationMapper={"In Progress"}
                                    handleInputChange={"In Progress"}
                                    assignValuesNested={"In Progress"}
                                    errors={false} // TODO: Update this to be dynamic.
                                    // Will move these^ down into OnBoardingSubmissionViewPage
                                    formSubmission={currentFormSubmission}
                                    formSubmissionData={currentFormSubmissionData}
                                    // footer={"In Progress"}
                                    totalPagesArray={totalPagesArray}
                                    currentPage={currentPage}
                                    setCurrentPage={setCurrentPage}
                                    overviewPageData={globalOverviewState[index + 1]}
                                    fidArray={fidArray}
                                    globalOverviewState={globalOverviewState}
                                    addToGlobalOverviewState={addToGlobalOverviewState}
                                    breadcrumbItems={breadcrumbItems}
                                    status={status}
                                    solutionId={solutionId}
                                    isEditingDraftLineItem={initialFormSubmission?.status !== SubmissionStages.Draft && currentFormSubmission?.status === SubmissionStages.Draft}
                                    onBackToOverview={revertToInitialState}
                                />
                            </ConditionalDisplay>
                        ))}
                    </ConditionalDisplay>
                    <RoleBasedPage authorizedRoles={["Creator", "Viewer", "Contributor", "Designer", "End User"]} solutionId={solutionId} workflowEmails={allWorkflowEmails}>
                        <ConditionalDisplay condition={initialFormSubmission?.status !== SubmissionStages.Draft && currentFormSubmission?.status !== SubmissionStages.Draft}>
                            <ApprovalOptions
                                checkErrors={checkErrors}
                                style={{ display: "flex" }}
                                solutionId={solutionId}
                                formSubmission={currentFormSubmission}
                                inputs={inputs}
                                isActiveRevision={isActiveRevision}
                                isOnBoardingFormApproval={true}
                                returnToRequestOverview={revertToInitialState}
                            />
                            <OverviewStatus
                                formSubmission={currentFormSubmission}
                                handleFormApprovalPageChange={handleFormApprovalPageChange}
                                initialFormSubmission={initialFormSubmission}
                                initialFormSubmissionData={initialFormSubmissionData}
                                returnToRequestOverview={revertToInitialState}
                            />
                            <ConditionalDisplay condition={currentFormSubmissionId !== fid}>
                                <OnboardingHeaderDetails
                                    initialFormSubmissionData={initialFormSubmissionData}
                                />
                            </ConditionalDisplay>
                            <OnBoardingFormApproval
                                fid={currentFormSubmissionId}
                                metadata={currentFormMetadata}
                                formSubmissionData={currentFormSubmissionData}
                                setFormSubmissionData={setCurrentFormSubmissionData}
                                initialConditions={currentFormConditions}
                                currentFormSubmission={currentFormSubmission}
                                formDefinitionData={currentFormSubmission?.formDefinition}
                            />
                            <LineItemsDashboard
                                formSubmission={currentFormSubmission}
                                handleFormApprovalPageChange={handleFormApprovalPageChange}
                            />
                            <OnboardingAuditHistoryDashboard
                                formSubmission={currentFormSubmission}
                                formTransactions={currentFormSubmission?.formTransactions}
                                formMetadata={currentFormMetadata}
                                loading={loading}
                            />
                        </ConditionalDisplay>
                    </RoleBasedPage>
                </PageContainer>
            </OnBoardingContextProvider>
        </AuthenticatedTemplate>
    )
}

export async function getServerSideProps(context) {

    const dencryptToken = getAccessToken(context.req)

    const { id, lineItemId, fid } = context.params

    if (!dencryptToken) {
        return {
            props: {
                data: []
            }
        }
    }

    const bearer = `Bearer ${dencryptToken}`;

    const config = {
        headers: {
            ContentType: 'application/json',
            Authorization: bearer
        },
    }

    try {
        const resFormDefinition = await getFormDefinitionWithConfig(id, fid, config)
        const resFormSubmission = await getFormSubmission(fid, config)

        return {
            props: {
                id,
                fid,
                status: resFormDefinition.data.status,
                metadata: resFormDefinition.data.metadata.metadata.form ?? {},
                initialConditions: resFormDefinition.data.metadata.conditions ?? {},
                initialFormSubmission: resFormSubmission.data.formSubmission,
                initialFormSubmissionData: resFormSubmission.data.formSubmissionData.data,
                lineItemId: lineItemId
            }
        }
    } catch (err) {
        console.error(err)
        return {
            props: {
                data: []
            }
        }
    }
}