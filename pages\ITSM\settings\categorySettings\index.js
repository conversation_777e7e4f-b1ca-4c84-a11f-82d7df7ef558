// Components
import { PageContainer } from '../../../../components/UI/Page/PageContainer/PageContainer'
import { useRouter } from 'next/router'
import { InputNumber } from 'primereact/inputnumber'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useState } from 'react'
import { InputMask } from 'primereact/inputmask'
import { ConditionalDisplay } from '../../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import Modal from '../../../../components/UI/Modal/Modal'
import TextInput from '../../../../components/UI/Input/TextInput/TextInput'
import PrimaryButton from '../../../../components/UI/PrimaryButton/PrimaryButton'
import Backarrow from '../../../../svg/metronic/back_metronic.svg'
import BreadCrumbs from '../../../../components/UI/BreadCrumbs/BreadCrumbs'
import Image from 'next/image'

// Images
import CategoryInfoActionButton from '../../../../svg/Main Dashboard/edit_sub.svg'
import DeleteIcon from '../../../../svg/metronic/delete.svg'
import AddIcon from '../../../../svg/metronic/plus.svg'

// Styles
import styles from './index.module.css'

export default function CategorySettings() {
  const router = useRouter()
  const [isUserEditingCategory, setIsUserEditingCategory] = useState(false)

  return (
    <PageContainer>
      <div className={styles.breadcrumbsContainer}>
        <Image
          src={Backarrow}
          alt="Back"
          onClick={() => (isUserEditingCategory ? setIsUserEditingCategory(false) : router.back())}
          className={styles.backArrow}
        />
        <BreadCrumbs
          title={'Category Settings'}
          breadcrumbItems={[{ label: `Settings` }, { label: `Category Settings` }]}
          theme="metronic"
        />
      </div>
      <ConditionalDisplay condition={!isUserEditingCategory}>
        <CategoryInformation setIsUserEditingCategory={setIsUserEditingCategory} />
        <WeightValueAssignments />
        <TierRanges />
      </ConditionalDisplay>
      <ConditionalDisplay condition={isUserEditingCategory}>
        <CategoryEditForm setIsUserEditingCategory={setIsUserEditingCategory} />
      </ConditionalDisplay>
    </PageContainer>
  )
}

const inputStyle = {
  // Needed to override the default PrimeReact input styles. Gives the InputNumber the metronic theme.
  padding: '10px',
  paddingLeft: '20px',
  fontSize: '16px',
  border: '1px solid #5151511a',
  borderRadius: '10px',
  height: '45px',
  boxSizing: 'border-box'
}

const CategorySettingsHeader = ({ header }) => {
  return (
    <div className={styles.headerContainer}>
      <div className={styles.categorySettingsHeader}>
        <h2>{header}</h2>
      </div>
      <div className={styles.categorySettingsHeaderUnderline}></div>
    </div>
  )
}

const WeightValueCalculator = ({
  categoryWeight,
  setCategoryWeight,
  subcategoryWeight,
  setSubcategoryWeight,
  affectedUsersWeight,
  setAffectedUsersWeight
}) => {
  return (
    <div className={styles.weightValueAssignment}>
      <div className={styles.weightValueAssignmentContainer}>
        <label className={styles.weightValueAssignmentLabel}>Category</label>
        <InputNumber
          value={categoryWeight}
          onChange={(e) => setCategoryWeight(e.value)}
          inputStyle={inputStyle}
          suffix="%"
          min={0}
          max={100}
        />
      </div>
      <div className={styles.weightValueAssignmentContainer}>
        <label className={styles.weightValueAssignmentLabel}>Subcategory</label>
        <InputNumber
          value={subcategoryWeight}
          onChange={(e) => setSubcategoryWeight(e.value)}
          inputStyle={inputStyle}
          suffix="%"
          min={0}
          max={100}
        />
      </div>
      <div className={styles.weightValueAssignmentContainer}>
        <label className={styles.weightValueAssignmentLabel}>Affected Users</label>
        <InputNumber
          value={affectedUsersWeight}
          onChange={(e) => setAffectedUsersWeight(e.value)}
          inputStyle={inputStyle}
          suffix="%"
          min={0}
          max={100}
        />
      </div>
    </div>
  )
}

const SeverityTestCalculator = ({ categoryWeight, subcategoryWeight, affectedUsersWeight }) => {
  const [categoryScore, setCategoryScore] = useState(0)
  const [subcategoryScore, setSubcategoryScore] = useState(0)
  const [affectedUsersScore, setAffectedUsersScore] = useState(0)

  // Calculate final severity score using the formula
  const finalSeverityScore = (
    (categoryWeight / 100) * categoryScore +
    (subcategoryWeight / 100) * subcategoryScore +
    (affectedUsersWeight / 100) * affectedUsersScore
  ).toFixed(2)

  return (
    <>
      <div className={styles.severityTestCalculatorHeader}>Severity Test Calculator</div>
      <p className={styles.severityTestCalculatorFormula}>
        Severity Score = (CategoryWeight x CategorySeverityScore) + (SubCategoryWeight x Sub-CategorySeverityScore) + (AffectedUserWeight x
        AffectedUserSeverityScore)
      </p>
      <div className={styles.weightValueAssignment}>
        <div className={styles.weightValueAssignmentContainer}>
          <label className={styles.weightValueAssignmentLabel}>Category Score</label>
          <InputNumber value={categoryScore} onChange={(e) => setCategoryScore(e.value)} inputStyle={inputStyle} />
        </div>
        <div className={styles.weightValueAssignmentContainer}>
          <label className={styles.weightValueAssignmentLabel}>Subcategory Score</label>
          <InputNumber value={subcategoryScore} onChange={(e) => setSubcategoryScore(e.value)} inputStyle={inputStyle} />
        </div>
        <div className={styles.weightValueAssignmentContainer}>
          <label className={styles.weightValueAssignmentLabel}>Affected Users Score</label>
          <InputNumber value={affectedUsersScore} onChange={(e) => setAffectedUsersScore(e.value)} inputStyle={inputStyle} />
        </div>
        <div className={styles.weightValueAssignmentContainer}>
          <label className={styles.weightValueAssignmentLabel}>Final Severity Score</label>
          <InputNumber value={parseFloat(finalSeverityScore)} inputStyle={inputStyle} readOnly />
        </div>
      </div>
    </>
  )
}

const RangeValueFields = () => {
  const [severity1Value, setSeverity1Value] = useState('1-4')
  const [severity2Value, setSeverity2Value] = useState('5-7')
  const [severity3Value, setSeverity3Value] = useState('8-10')
  const [showModal, setShowModal] = useState(false)

  return (
    <div className={styles.rangeValueFieldsContainer}>
      <div className={styles.rangeValueFieldsItem}>
        <label className={styles.rangeValueFieldsLabel}>Severity 1 Range Value</label>
        <InputMask value={severity1Value} onChange={(e) => setSeverity1Value(e.value)} mask="9-9" style={inputStyle} />
      </div>
      <div className={styles.rangeValueFieldsItem}>
        <label className={styles.rangeValueFieldsLabel}>Severity 2 Range Value</label>
        <InputMask value={severity2Value} onChange={(e) => setSeverity2Value(e.value)} mask="9-9" style={inputStyle} />
      </div>
      <div className={styles.rangeValueFieldsItem}>
        <label className={styles.rangeValueFieldsLabel}>Severity 3 Range Value</label>
        <InputMask value={severity3Value} onChange={(e) => setSeverity3Value(e.value)} mask="9-9" style={inputStyle} />
      </div>
      <div className={styles.rangeValueFieldsItem}>
        <PrimaryButton text="Configure Impacted User Values" width={280} height={45} onClick={() => setShowModal(true)} />
      </div>
      <ImpactedUserValuesModal visible={showModal} onHide={() => setShowModal(false)} />
    </div>
  )
}

const ImpactedUserValuesModal = ({ visible, onHide }) => {
  const [userValues, setUserValues] = useState([
    { id: 1, title: 'Individual', value: 4 },
    { id: 2, title: 'Less Than 10 Users', value: 6 },
    { id: 3, title: 'Greater Than 10 Users', value: 7 },
    { id: 4, title: '50-200 Users', value: 9 },
    { id: 5, title: 'Whole Company', value: 10 }
  ])

  const handleValueChange = (id, newValue) => {
    setUserValues(userValues.map((item) => (item.id === id ? { ...item, value: newValue } : item)))
  }

  const handleTitleChange = (id, newTitle) => {
    setUserValues(userValues.map((item) => (item.id === id ? { ...item, title: newTitle } : item)))
  }

  const addNewUserValue = () => {
    const newId = Math.max(...userValues.map((item) => item.id)) + 1
    setUserValues([...userValues, { id: newId, title: '', value: 0 }])
  }

  const deleteUserValue = (id) => {
    setUserValues(userValues.filter((item) => item.id !== id))
  }

  return (
    <Modal header="Impacted User Values" visible={visible} onHide={onHide} width={50} backgroundColor="white" theme="metronic">
      <div className={styles.modalContent}>
        <p className={styles.modalInstructions}>Assign the weights to the affected users and create new ones if necessary</p>

        <div className={styles.modalSection}>
          <h4 className={styles.modalSectionTitle}>Amount Of Users Affected</h4>
          <div className={styles.modalTableHeader}>
            <span className={styles.modalTableHeaderTitle}>Title:</span>
          </div>

          <div className={styles.modalUserValuesContainer}>
            {userValues.map((item) => (
              <div key={item.id} className={styles.modalUserValueRow}>
                <div className={styles.modalUserValueItem}>
                  <TextInput
                    value={item.title}
                    onChange={(e) => handleTitleChange(item.id, e.value)}
                    theme="metronic"
                    placeholder="Enter title"
                  />
                </div>
                <div className={styles.modalUserValueItem}>
                  <InputNumber
                    value={item.value}
                    onChange={(e) => handleValueChange(item.id, e.value)}
                    inputStyle={inputStyle}
                    min={0}
                    max={10}
                  />
                </div>
                <div className={styles.modalUserValueAction}>
                  <Image
                    src={DeleteIcon}
                    alt="Delete"
                    width={30}
                    height={30}
                    onClick={() => deleteUserValue(item.id)}
                    className={styles.deleteIcon}
                  />
                </div>
              </div>
            ))}
          </div>

          <div className={styles.modalActions}>
            <button className={styles.addNewUserValueButton} onClick={addNewUserValue}>
              <Image src={AddIcon} alt="Add" width={30} height={30} />
              Add New User Value
            </button>
          </div>
        </div>

        <div className={styles.modalFooter}>
          <PrimaryButton text="Create Queue" width={150} height={45} onClick={onHide} />
        </div>
      </div>
    </Modal>
  )
}

const SLATargetFields = () => {
  const [tier1SLA, setTier1SLA] = useState({
    months: 0,
    weeks: 0,
    days: 0,
    hours: 0,
    minutes: 0
  })
  const [tier2SLA, setTier2SLA] = useState({
    months: 0,
    weeks: 0,
    days: 0,
    hours: 0,
    minutes: 0
  })
  const [tier3SLA, setTier3SLA] = useState({
    months: 0,
    weeks: 0,
    days: 0,
    hours: 0,
    minutes: 0
  })

  return (
    <div className={styles.tierSLAContainer}>
      <div className={styles.rangeValueFieldsItem}>
        <label className={styles.rangeValueFieldsLabel}>Tier 1 SLA Target</label>
        <div className={styles.tierSLAItem}>
          <InputNumber
            value={tier1SLA.months}
            onChange={(e) => setTier1SLA({ ...tier1SLA, months: e.value })}
            suffix="'M"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier1SLA.weeks}
            onChange={(e) => setTier1SLA({ ...tier1SLA, weeks: e.value })}
            suffix="'W"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier1SLA.days}
            onChange={(e) => setTier1SLA({ ...tier1SLA, days: e.value })}
            suffix="'D"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier1SLA.hours}
            onChange={(e) => setTier1SLA({ ...tier1SLA, hours: e.value })}
            suffix="'H"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier1SLA.minutes}
            onChange={(e) => setTier1SLA({ ...tier1SLA, minutes: e.value })}
            suffix="'m"
            inputStyle={inputStyle}
          />
        </div>
      </div>
      <div className={styles.rangeValueFieldsItem}>
        <label className={styles.rangeValueFieldsLabel}>Tier 2 SLA Target</label>
        <div className={styles.tierSLAItem}>
          <InputNumber
            value={tier2SLA.months}
            onChange={(e) => setTier2SLA({ ...tier2SLA, months: e.value })}
            suffix="'M"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier2SLA.weeks}
            onChange={(e) => setTier2SLA({ ...tier2SLA, weeks: e.value })}
            suffix="'W"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier2SLA.days}
            onChange={(e) => setTier2SLA({ ...tier2SLA, days: e.value })}
            suffix="'D"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier2SLA.hours}
            onChange={(e) => setTier2SLA({ ...tier2SLA, hours: e.value })}
            suffix="'H"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier2SLA.minutes}
            onChange={(e) => setTier2SLA({ ...tier2SLA, minutes: e.value })}
            suffix="'m"
            inputStyle={inputStyle}
          />
        </div>
      </div>
      <div className={styles.rangeValueFieldsItem}>
        <label className={styles.rangeValueFieldsLabel}>Tier 3 SLA Target</label>
        <div className={styles.tierSLAItem}>
          <InputNumber
            value={tier3SLA.months}
            onChange={(e) => setTier3SLA({ ...tier3SLA, months: e.value })}
            suffix="'M"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier3SLA.weeks}
            onChange={(e) => setTier3SLA({ ...tier3SLA, weeks: e.value })}
            suffix="'W"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier3SLA.days}
            onChange={(e) => setTier3SLA({ ...tier3SLA, days: e.value })}
            suffix="'D"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier3SLA.hours}
            onChange={(e) => setTier3SLA({ ...tier3SLA, hours: e.value })}
            suffix="'H"
            inputStyle={inputStyle}
          />
          <InputNumber
            value={tier3SLA.minutes}
            onChange={(e) => setTier3SLA({ ...tier3SLA, minutes: e.value })}
            suffix="'m"
            inputStyle={inputStyle}
          />
        </div>
      </div>
    </div>
  )
}

const CategoryEditForm = () => {
  return (
    <>
      <CategoryEditInformation />
      <SubCategoriesEdit />
    </>
  )
}

const CategoryEditInformation = () => {
  const [categoryName, setCategoryName] = useState('')
  const [severityScore, setSeverityScore] = useState(0)

  return (
    <>
      <CategorySettingsHeader header="Category Information" />
      <div className={styles.categoryEditContainer}>
        <div className={styles.categoryEditFormRow}>
          <div className={styles.categoryEditFormGroup}>
            <label className={styles.rangeValueFieldsLabel}>Category Name</label>
            <TextInput value={categoryName} onChange={(e) => setCategoryName(e.value)} theme="metronic" placeholder="Enter category name" />
          </div>
          <div className={styles.categoryEditFormGroup}>
            <label className={styles.rangeValueFieldsLabel}>Severity Score</label>
            <InputNumber value={severityScore} onChange={(e) => setSeverityScore(e.value)} inputStyle={inputStyle} min={0} max={10} />
          </div>
        </div>

        <div>
          <div className={styles.categoryEditMetadataRow}>
            <div className={styles.categoryEditMetadataGroup}>
              <label className={styles.categoryEditMetadataLabel}>Created By</label>
              <span className={styles.categoryEditMetadataValue}>$user</span>
            </div>
            <div className={styles.categoryEditMetadataGroup}>
              <label className={styles.categoryEditMetadataLabel}>Created</label>
              <span className={styles.categoryEditMetadataValue}>07/22/2025</span>
            </div>
            <div className={styles.categoryEditMetadataGroup}>
              <label className={styles.categoryEditMetadataLabel}>Modified By</label>
              <span className={styles.categoryEditMetadataValue}>07/22/2025</span>
            </div>
            <div className={styles.categoryEditMetadataGroup}>
              <label className={styles.categoryEditMetadataLabel}>Date Modified</label>
              <span className={styles.categoryEditMetadataValue}>--</span>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const SubCategoriesEdit = () => {
  const [subCategories, setSubCategories] = useState([{ id: 1, name: '', severityScore: 0 }])

  const addNewSubCategory = () => {
    const newId = Math.max(...subCategories.map((item) => item.id)) + 1
    setSubCategories([...subCategories, { id: newId, name: '', severityScore: 0 }])
  }

  const deleteSubCategory = (id) => {
    setSubCategories(subCategories.filter((item) => item.id !== id))
  }

  const updateSubCategory = (id, field, value) => {
    setSubCategories(subCategories.map((item) => (item.id === id ? { ...item, [field]: value } : item)))
  }

  return (
    <>
      <CategorySettingsHeader header="Sub-Categories" />
      <div className={styles.subCategoriesContainer}>
        <div className={styles.subCategoriesContent}>
          {subCategories.map((subCategory) => (
            <div key={subCategory.id} className={styles.subCategoryRow}>
              <div className={styles.subCategoryItem}>
                <label className={styles.rangeValueFieldsLabel}>Sub-Category Name</label>
                <TextInput
                  value={subCategory.name}
                  onChange={(e) => updateSubCategory(subCategory.id, 'name', e.value)}
                  theme="metronic"
                  placeholder="Enter sub-category name"
                />
              </div>
              <div className={styles.subCategoryItem}>
                <label className={styles.rangeValueFieldsLabel}>Severity Score</label>
                <div className={styles.subCategoryItemSeverityScore}>
                  <InputNumber
                    value={subCategory.severityScore}
                    onChange={(e) => updateSubCategory(subCategory.id, 'severityScore', e.value)}
                    inputStyle={inputStyle}
                    min={0}
                    max={10}
                    style={{ width: '100%' }}
                  />
                  <Image
                    src={DeleteIcon}
                    alt="Delete"
                    width={30}
                    height={30}
                    onClick={() => deleteSubCategory(subCategory.id)}
                    className={styles.deleteIcon}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className={styles.subCategoriesActions}>
          <button className={styles.addNewUserValueButton} onClick={addNewSubCategory}>
            <Image src={AddIcon} alt="Add" width={30} height={30} />
            Add New Sub-Category
          </button>
        </div>
      </div>
    </>
  )
}

const CategoryInformation = ({ setIsUserEditingCategory }) => {
  const categoryInfoTableHeader = (
    <div className={styles.categoryInformationTableHeader}>
      <h3>Category Information</h3>
      <PrimaryButton text="+ Create New Category" width={200} />
    </div>
  )

  const actionTemplate = (rowData) => {
    return (
      <div>
        <Image
          src={CategoryInfoActionButton}
          alt="Edit"
          width={20}
          height={20}
          onClick={() => setIsUserEditingCategory(true)}
          style={{ cursor: 'pointer' }}
        />
      </div>
    )
  }

  const categoryInfoData = [
    {
      categoryName: 'Software',
      subcategories: '5',
      categorySeverityScore: '7',
      createdBy: 'Matteo Gobeaux',
      lastUpdated: '9/23/24'
    },
    {
      categoryName: 'Hardware',
      subcategories: '3',
      categorySeverityScore: '5',
      createdBy: 'Matteo Gobeaux',
      lastUpdated: '9/23/24'
    }
  ]

  return (
    <>
      <CategorySettingsHeader header="Category Information" />
      <div className={styles.settingsInformationContent}>
        <p>
          Create new categories using the “Create New Category” button, from there you can also create sub-categories. If you need to add
          more sub-categories to an existing category, simply click the edit icon next to the desired category.
        </p>
        <p className={styles.categoryInformationContent}>
          Categories/subcategories will NOT appear on forms until they are assigned to a queue.
        </p>
      </div>
      <DataTable value={categoryInfoData} className="custom-lead" header={categoryInfoTableHeader} style={{ marginBottom: '10px' }}>
        <Column field="action" header="Action" body={actionTemplate} style={{ width: '5%' }} />
        <Column field="categoryName" header="Category Name" />
        <Column field="subcategories" header="Subcategories" />
        <Column field="categorySeverityScore" header="Category Severity Score" />
        <Column field="createdBy" header="Created By" />
        <Column field="lastUpdated" header="Last Updated" />
      </DataTable>
    </>
  )
}

const WeightValueAssignments = () => {
  const [categoryWeight, setCategoryWeight] = useState(40)
  const [subcategoryWeight, setSubcategoryWeight] = useState(30)
  const [affectedUsersWeight, setAffectedUsersWeight] = useState(30)

  return (
    <>
      <CategorySettingsHeader header="Weight Value Assignments" />
      <div className={styles.settingsInformationContent}>
        <p>Determine the weight of each section.</p>
        <p>
          You can test out the values placed for the weights and each category, subcategory, and affected users scores using the calculator
          below.
        </p>
      </div>
      <WeightValueCalculator
        categoryWeight={categoryWeight}
        setCategoryWeight={setCategoryWeight}
        subcategoryWeight={subcategoryWeight}
        setSubcategoryWeight={setSubcategoryWeight}
        affectedUsersWeight={affectedUsersWeight}
        setAffectedUsersWeight={setAffectedUsersWeight}
      />
      <SeverityTestCalculator
        categoryWeight={categoryWeight}
        subcategoryWeight={subcategoryWeight}
        affectedUsersWeight={affectedUsersWeight}
      />
    </>
  )
}

const TierRanges = () => {
  return (
    <>
      <CategorySettingsHeader header="Tier Ranges" />
      <p>
        Please input a number range for the values of the tiers, this will determine the range for tickets based on the total severity
        score.
      </p>
      <RangeValueFields />
      <SLATargetFields />
    </>
  )
}
