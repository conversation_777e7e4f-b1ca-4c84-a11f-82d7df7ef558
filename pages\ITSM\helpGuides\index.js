import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import { useRouter } from 'next/router'
import { useState, useEffect } from 'react'
import Button from '../../../components/UI/Button/Button'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Image from 'next/image'

import HelpGuidesBackground from '../../../images/itsm_help_guides_background.png'

import styles from './index.module.css'
// TODO: Rename the rowOne, rowTwo, etc. to something more descriptive.

// TODO: Replace this mock data when the API is implemented.
const helpGuidesData = {
  'Work Accounts': {
    icon: 'pi-database',
    color: '#4CAF50',
    articles: ['How To Reset A Password', 'How To Contact Support', 'How To Change Your Password', 'How To Setup 2FA', 'How To Reset 2FA']
  },
  'Setting up a website': {
    icon: 'pi-server',
    color: '#00BCD4',
    articles: [
      'How to register a domain name',
      'How to set up a hosting account',
      'How to install and configure a CMS',
      'How to upload and manage website ...',
      'Tips for optimizing website performa ...'
    ]
  },
  'Security and privacy': {
    icon: 'pi-shield',
    color: '#E91E63',
    articles: [
      'How To Report Emails',
      'What is SSL and why is it important?',
      'How to enable website access',
      'How to protect sensitive information ...',
      'Tips for avoiding common security ...'
    ]
  }
}

export default function HelpGuides() {
  const router = useRouter()
  const [currentView, setCurrentView] = useState('main')
  const [currentArticle, setCurrentArticle] = useState(null)

  useEffect(() => {
    const { article } = router.query
    if (article) {
      setCurrentView('article')
      setCurrentArticle(article)
    } else {
      setCurrentView('main')
      setCurrentArticle(null)
    }
  }, [router.query])

  const handleArticleClick = (articleTitle) => {
    const articleSlug = articleTitle
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w-]/g, '')
    router.push(`/ITSM/helpGuides?article=${articleSlug}`, undefined, { shallow: true })
  }

  const handleBackClick = () => {
    if (currentView === 'article') {
      router.push('/ITSM/helpGuides', undefined, { shallow: true })
    } else {
      router.back()
    }
  }

  return (
    <PageContainer theme="metronic">
      <div className={styles.breadCrumbContainer}>
        <Image src={Backarrow} alt="Back" onClick={handleBackClick} className={styles.backArrow} />
        <BreadCrumbs
          title="Help Guides"
          breadcrumbItems={currentView === 'article' ? [{ label: 'Dashboard' }, { label: 'Article' }] : [{ label: 'Dashboard' }]}
          theme="metronic"
        />
      </div>

      <div className={styles.helpGuidesContainer} style={{ backgroundImage: `url(${HelpGuidesBackground.src})` }}>
        <div className={styles.rowTwo}>
          <div className={styles.rowTwoLeft}>
            <div className={styles.rowOne}>
              <div>
                <h1>Help Guides</h1>
                <p>Browse through our frequently asked questions, tutorials, and other self-help resources to find the answers you need.</p>
              </div>
            </div>
            <SearchBox />
            <div className={styles.rowThree}>
              <strong>POPULAR:</strong>
              <a href="#">How To Reset Password?</a>
              <a href="#">How To Get Access For A Figma?</a>
            </div>
          </div>
          <SubmitCard />
          <SubmitButton />
        </div>
      </div>

      {currentView === 'main' ? (
        <HelpGuideCards categoriesData={helpGuidesData} onArticleClick={handleArticleClick} />
      ) : (
        <ArticleView articleSlug={currentArticle} />
      )}
    </PageContainer>
  )
}

const SearchBox = () => (
  <div className={styles.searchBox}>
    <input type="text" placeholder="Search" />
    <button>Search</button>
  </div>
)

const SubmitCard = () => (
  <div className={styles.submitCard}>
    <h3>Still have questions? We&apos;re here to help!</h3>
    <p>
      Chat with our support team by clicking the chat button in the right.
      <br />
      <p>We’re here to assist you!</p>
    </p>
  </div>
)

const SubmitButton = () => (
  <div style={{ alignSelf: 'center', marginTop: '4rem' }}>
    <Button
      label="Submit A Ticket"
      className={styles.submitButton}
      width="300px"
      height="50px"
      theme="metronic"
      icon={<i className="pi pi-comment" />}
    />
  </div>
)

const HelpGuideCards = ({ categoriesData, onArticleClick }) => {
  const handleArticleClick = (article) => {
    if (article.includes('How to protect sensitive information')) {
      onArticleClick(article)
    }
  }

  return (
    <div className={styles.helpCardsContainer}>
      {Object.entries(categoriesData).map(([categoryName, categoryInfo]) => (
        <div key={categoryName} className={styles.helpCard}>
          <div className={styles.cardHeader}>
            <div className={styles.cardIcon} style={{ backgroundColor: categoryInfo.color }}>
              <i className={`pi ${categoryInfo.icon}`}></i>
            </div>
            <h3 className={styles.cardTitle}>{categoryName}</h3>
          </div>
          <div className={styles.cardContent}>
            {categoryInfo.articles.map((article, index) => (
              <div
                key={index}
                className={styles.articleItem}
                onClick={() => handleArticleClick(article)}
                style={{
                  cursor: article.includes('How to protect sensitive information') ? 'pointer' : 'default'
                }}
              >
                <i className="pi pi-file"></i>
                <span>{article}</span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

// TODO: Replace this once the API is implemented.
const ArticleView = ({ articleSlug }) => {
  return (
    <div className={styles.articleContainer}>
      <h1 className={styles.articleTitle}>How To Protect Sensitive Information</h1>

      <div className={styles.articleContent}>
        <p>
          Keeping your personal and business information secure is essential. Whether you&apos;re handling customer data, financial records, or
          internal communications, protecting sensitive information helps prevent identity theft, fraud, and data breaches.
        </p>
        <p>This article outlines practical steps you can take to safeguard your information.</p>

        <h3>What is Sensitive Information?</h3>
        <p>Sensitive information includes:</p>
        <ul>
          <li>
            Personally identifiable information (PII): e.g., full name, Social Security number, address, phone number, email, date of birth
          </li>
          <li>Financial data: e.g., bank account details, credit card numbers</li>
          <li>Login credentials: e.g., usernames, passwords, security questions</li>
          <li>Health records: e.g., medical history, insurance details</li>
          <li>Confidential business data: e.g., trade secrets, contracts, proprietary strategies</li>
        </ul>

        <h3>Best Practices to Protect Sensitive Information</h3>

        <h4>1. Use Strong Passwords</h4>
        <ul>
          <li>Create unique, complex passwords for each account</li>
          <li>Use a mix of uppercase/lowercase letters, numbers, and symbols</li>
          <li>Avoid using easily guessed information (like your birthday or pet&apos;s name)</li>
          <li>Consider using a reputable password manager</li>
        </ul>

        <h4>2. Enable Two-Factor Authentication (2FA)</h4>
        <ul>
          <li>Add an extra layer of protection by requiring a second form of verification</li>
          <li>Common methods: text message codes, authentication apps, biometric verification</li>
          <li>Limit sharing</li>
          <li>Only share sensitive data with people who absolutely need it</li>
          <li>Use role-based access control (RBAC) to restrict information by user type</li>
          <li>Revoke access promptly when no longer needed</li>
        </ul>

        <h4>3. Be Cautious with Email and Links</h4>
        <ul>
          <li>Do not open attachments or click links from unknown or suspicious sources</li>
          <li>Watch for phishing attempts that mimic trusted organizations</li>
          <li>Verify the sender before sharing any information</li>
        </ul>

        <h4>4. Keep Software Updated</h4>
        <ul>
          <li>Apply security patches and updates as soon as they are available</li>
          <li>This includes your operating system, browsers, antivirus software, and apps</li>
        </ul>

        <h4>5. Encrypt Sensitive Data</h4>
        <ul>
          <li>Use encryption tools for storing or sharing confidential files</li>
          <li>Look for HTTPS in web addresses before submitting information online</li>
        </ul>

        <h4>6. Secure Your Devices</h4>
        <ul>
          <li>Use passwords or biometric locks on all devices</li>
          <li>Enable auto-lock and remote wipe features</li>
          <li>Avoid using public Wi-Fi without a VPN</li>
        </ul>

        <h4>7. Disposal of Data Safely</h4>
        <ul>
          <li>Shred physical documents containing sensitive info</li>
          <li>Use secure deletion methods for digital files and storage devices</li>
        </ul>

        <h3>What To Do If You Suspect a Data Breach</h3>
        <p>If you think your information has been compromised:</p>
        <ol>
          <li>Change passwords immediately</li>
          <li>Notify your organization&apos;s IT or security team</li>
          <li>Monitor your accounts for suspicious activity</li>
          <li>Report the incident to the appropriate authorities or services (e.g., banks, email providers)</li>
        </ol>

        <h3>Need Help?</h3>
        <p>
          If you&apos;re unsure whether something is safe to share or need help protecting your information, please contact our support team or
          consult our security policy.
        </p>

        <div className={styles.articleFooter}>
          <div className={styles.helpfulSection}>
            <p>Is this article helpful?</p>
            <div className={styles.helpfulButtons}>
              <button className={styles.thumbsButton}>
                <i className="pi pi-thumbs-up"></i>
              </button>
              <button className={styles.thumbsButton}>
                <i className="pi pi-thumbs-down"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
