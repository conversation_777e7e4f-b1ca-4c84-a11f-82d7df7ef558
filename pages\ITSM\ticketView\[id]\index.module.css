.headerContainer {
  margin-bottom: 20px;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.backArrow {
  cursor: pointer;
  width: 45px;
  height: 45px;
  transition: opacity 0.2s ease;
}

.backArrow:hover {
  opacity: 0.7;
}

.ticketTitle {
  color: #012853;
  font-size: 2rem;
  font-weight: 600;
  line-height: 1.2;
}

.titleUnderline {
  width: 100%;
  height: 3px;
  background-color: #012853;
}

.ticketDetailsContainer {
  background-color: rgba(0, 157, 255, 0.07);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  align-items: start;
}

.detailItem {
  display: flex;
  gap: 5px;
}

.detailLabel {
  font-weight: 600;
  font-size: 0.85rem;
  color: #666666;
  margin-bottom: 2px;
}

.detailValue {
  font-size: 0.85rem;
  color: #333333;
  font-weight: 700;
}

.ticketWidgetsContainer {
  display: flex;
  gap: 10px;
}

/* Visual Workflow Styles */
.workflowContainer {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 10px;
  border: 1px solid #e0e0e0;
  width: 70%;
}

.workflowHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflowTitle {
  color: #012853;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.currentStatus {
  font-size: 0.85rem;
  color: #666666;
}

.statusValue {
  color: #28A745;
  font-weight: 600;
  font-size: 0.85rem;
  background-color: rgba(40, 167, 69, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.workflowStages {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  gap: 20px;
}

.stageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.stageContent {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  background-color: white;
  margin-bottom: 15px;
}

.stageIcon {
  width: 45px;
  height: 45px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
}

.iconText {
  font-size: 1.2rem;
}

.iconImage {
  object-fit: contain;
}

.stageLabels {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stageName {
  font-weight: 600;
  font-size: 0.85rem;
  color: #012853;
  margin-bottom: 2px;
}

.stageStatus {
  font-size: 0.85rem;
  color: #666666;
  font-weight: 500;
}

.progressBars {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 8px;
}

.progressBar {
  flex: 1;
  height: 6px;
  border-radius: 2px;
}

.progressBarPrimary {
  background-color: #1B84FF;
}

.progressBarSecondary {
  background-color: rgba(27, 132, 255, 0.2);
}

/* Ticket Actions Styles */
.ticketActionsContainer {
  margin-top: 10px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  width: 30%;
}

.actionsTitle {
  color: #012853;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.actionsButtons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.actionButtonWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.actionIcon {
  position: absolute;
  left: 8px;
  z-index: 2;
  filter: brightness(0) invert(1);
}

.actionButton {
  padding-left: 30px !important;
}

/* Issue Information Styles */
.issueInformationContainer {
  margin-top: 10px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.issueTitle {
  color: #012853;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.formFieldsRow {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  align-items: flex-start;
}

.descriptionSection {
  margin-bottom: 10px;
}

.attachmentsSection {
  margin-top: 10px;
}

.attachmentsTitle {
  color: #012853;
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.attachmentsList {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.attachmentItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f9f9f9;
  min-width: 80px;
}

.attachmentImage {
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #ddd;
}

.attachmentFile {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.fileIcon {
  font-size: 24px;
}

.fileName {
  font-size: 10px;
  color: #666;
  text-align: center;
  word-break: break-all;
  max-width: 70px;
}

/* Relationships Dashboard Styles */
.relationshipsDashboardContainer {
  margin-top: 10px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 20px;
}

/* Comments Styles */
.commentsContainer {
  margin-top: 10px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 20px;
}

.tabMenuContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commentEditorContainer {
  margin-top: 10px;
}

/* Comments Section Styles */
.commentsSection {
  margin-top: 20px;
}

.commentItem {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
}

.commentItem:last-child {
  margin-bottom: 0;
}

.commentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.commentAuthorSection {
  display: flex;
  align-items: center;
}

.commentAuthorInfo {
  display: flex;
  flex-direction: column;
}

.commentAuthor {
  font-weight: 600;
  font-size: 1rem;
  color: #012853;
}

.commentTimestamp {
  font-size: 0.85rem;
  color: #666666;
}

.commentContent {
  margin-left: 56px; /* Align with author name */
}

.commentText {
  font-size: 0.9rem;
  color: #333333;
  line-height: 1.4;
  margin: 0 0 10px 0;
}

/* Comment Attachments Styles */
.commentAttachments {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}

.commentAttachmentsTitle {
  color: #012853;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.commentAttachmentsList {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.commentAttachmentItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background-color: white;
  min-width: 70px;
}

.commentAttachmentImage {
  border-radius: 3px;
  object-fit: cover;
  border: 1px solid #ccc;
}

.commentAttachmentFile {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.commentFileIcon {
  font-size: 20px;
}

.commentFileName {
  font-size: 9px;
  color: #666;
  text-align: center;
  word-break: break-all;
  max-width: 60px;
}

/* Audit Log Styles */
.auditLogContainer {
  margin-top: 20px;
}

.auditLogHeader { 
  background-color: #1b84ff0d;
  color: #515151;
  padding: 1rem;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
}