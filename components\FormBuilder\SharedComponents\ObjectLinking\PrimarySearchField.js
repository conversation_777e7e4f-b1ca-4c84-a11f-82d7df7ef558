import clsx from "clsx";
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions";
import { SettingsLabel } from "../../Settings/UI/SettingsLabel/SettingsLabel";
import { Dropdown } from "primereact/dropdown";
import { useFetchSourceForm } from "../../../../hooks/useFetchSourceForm";

import styles from "../../../../styles/BuilderSwimLaneStyles/SettingsMenu.module.css";
import { SolutionIds } from "../../../../src/contants";

// TODO: Check if the transformResultFields breaks the object link component.
export const PrimarySearchField = ({
  inputs,
  handleInputChange,
  createObjectLinkListTable,
  isSpreadsheet = false,
  isObjectLinkGrid = false,
  solutionId,
  loading,
}) => {
  const { createTargetObject } = useUtilityFunctions();
  const { filterObjectsByGuid, transformResultFields } = useFetchSourceForm();
  const isDisabled =
    (isSpreadsheet || isObjectLinkGrid) &&
    (!inputs?.sourceFormName || !inputs?.isObjectLinking);

  return (
    <>
      <SettingsLabel label="Primary Search Field" isRequired={true} />
      <Dropdown
        className={clsx(
          styles.fullWidth,
          "p-tempStyle",
          isDisabled && styles.disabledField
        )}
        name={`${inputs?.guid}.primarySearchField`}
        value={inputs?.primarySearchField ?? false}
        onChange={(e) => {
          handleInputChange(e);
          console.log(
            inputs,
            "inputs",
            transformResultFields(
              filterObjectsByGuid(e.value.guid, inputs?.sourceFormData?.data)
            )
          );
          console.log("primary e", e);
          handleInputChange(
            createTargetObject(
              `${inputs?.guid}.resultFields`,
              isSpreadsheet || isObjectLinkGrid
                ? transformResultFields(
                    filterObjectsByGuid(
                      e.value.guid,
                      inputs?.sourceFormData?.data
                    ),
                    isSpreadsheet
                  )
                : filterObjectsByGuid(
                    e.value.guid,
                    inputs?.sourceFormData?.data
                  )
            )
          );

          // isSpreadsheet &&
          //   createObjectLinkListTable(
          //     e,
          //     transformResultFields(
          //       filterObjectsByGuid(
          //         e.value.guid,
          //         inputs?.sourceFormData?.data
          //       ),isSpreadsheet
          //     )
          //   );
        }}
        options={inputs?.sourceFormData?.data || !inputs?.isObjectLinking}
        disabled={isDisabled}
        loading={loading}
      />
    </>
  );
};
