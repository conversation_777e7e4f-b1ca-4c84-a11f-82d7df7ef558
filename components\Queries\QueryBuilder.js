import clsx from "clsx";
import PrimaryButton from "../../components/UI/PrimaryButton/PrimaryButton";
import Button from "../../components/UI/Button/Button";
import Image from "next/image";
import React, { createElement, useState, useRef, useEffect } from "react";
import {
  createDefaultConditionGroup,
  defaultCondition,
  useQueriesPageContext,
} from "../../public/ContextProviders/QueriesPageContextProvider";
import { Guid } from "js-guid";
import { ConditionalDisplay } from "../UI/ConditionalDisplay/ConditionalDisplay";
import { useInputs } from "../../hooks/useInput";
import { Calendar } from "primereact/calendar";
import { Label } from "../../components/UI/Label/Label";
import { InputText } from "primereact/inputtext";
import { Dropdown } from "primereact/dropdown";
import { MultiSelect } from "primereact/multiselect";
import { InputNumber } from "primereact/inputnumber";
import { useFormConverters } from "../../hooks/useFormConverters";
import { toDateObject } from "../../utillites/dateFormat";
import { Toast } from "primereact/toast";
import { useModuleOptions } from "../../hooks/Queries/useModuleOptions";
import { clearSqlQueryExceptFormAndStatus } from "../../public/ContextProviders/QueriesPageContextProvider";
import { createInitialDataQueryInfo } from "../../public/ContextProviders/QueriesPageContextProvider";

// To-Do: This should be moved into separate components/hooks. Not the imports, but the actual components from these files. - Alex
import { InputContainer } from "./QuerySideNavbar";

import styles from "./QueryDashboard.module.css";

export default function QueryBuilder({ setIsVisibleBuilder, dataQuery, setDataQuery }) {
  const [hasAnyDuplicates, setHasAnyDuplicates] = useState(false);
  const currentDataQuery = Object.keys(dataQuery).length > 0 ? dataQuery : createInitialDataQueryInfo();
  
  const {
    sqlQuery,
    setSqlQuery,
    formMetadata,
    loadingFormMetadata,
    hasUserDecidedOnConditions,
    setHasUserDecidedOnConditions,
    module,
    handleRunQuery
  } = useQueriesPageContext();
  const toast = useRef();

  const createQuery = () => {
    if (hasMissingOrEmptyProperties(dataQuery)) {
      toast.current.show({
        severity: "error",
        summary: "Error",
        detail: "Please fill out all fields before creating conditions",
        life: 3000,
      });
      return;
    }


    if (hasUserDecidedOnConditions) {
      handleRunQuery(module)
    }

    setIsVisibleBuilder(false);
  };

  const createNoConditionals = () => {
    if (!hasUserDecidedOnConditions) {
      setHasUserDecidedOnConditions(true);
    }

    setSqlQuery(clearSqlQueryExceptFormAndStatus(sqlQuery))
    setDataQuery({})

    setIsVisibleBuilder(false);
  };

  return (
    <>
      <div className={styles.queryContainer}>
        <Toast ref={toast} />
        <DateRangeSection sqlQuery={sqlQuery} setSqlQuery={setSqlQuery} />
        <ConditionalsSection
          dataQuery={currentDataQuery}
          setDataQuery={setDataQuery}
          formMetadata={formMetadata}
          loadingFormMetadata={loadingFormMetadata}
          module={module}
          onDuplicateCheck={(hasDuplicates) => setHasAnyDuplicates(hasDuplicates)}
        />
        <ModalButtonsSection
          createQuery={createQuery}
          createNoConditionals={createNoConditionals}
          hasAnyDuplicates={hasAnyDuplicates}
        />
      </div>
    </>
  );
}

const DateRangeSection = ({ sqlQuery, setSqlQuery }) => {
  return (
    <div className={styles.dateRangeSectionContainer}>
      <InputContainer containerStyles={{ width: "25.6%" }}>
        <Label label="From Date" />
        <Calendar
          value={toDateObject(sqlQuery?.submissionDateFrom)}
          onChange={(e) =>
            setSqlQuery({ ...sqlQuery, submissionDateFrom: e.value })
          }
          maxDate={toDateObject(sqlQuery?.submissionDateTo)}
          showIcon
        />
      </InputContainer>
      <InputContainer containerStyles={{ width: "25.6%" }}>
        <Label label="To Date" />
        <Calendar
          value={toDateObject(sqlQuery?.submissionDateTo)}
          onChange={(e) =>
            setSqlQuery({ ...sqlQuery, submissionDateTo: e.value })
          }
          minDate={toDateObject(sqlQuery?.submissionDateFrom)}
          showIcon
        />
      </InputContainer>
    </div>
  );
};

const ConditionalsSection = ({
  dataQuery,
  setDataQuery,
  formMetadata,
  loadingFormMetadata,
  module,
  onDuplicateCheck,
}) => {
  const groupCount = Object.keys(dataQuery).length;

  const addGroup = () => {
    const newGroup = createDefaultConditionGroup(defaultCondition);

    setDataQuery((prevState) => ({
      ...prevState,
      [Guid.newGuid().toString()]: newGroup,
    }));
  };

  return (
    <div className={styles.conditionScrollContainer}>
      <label className={styles.labelStyles}>
        {module === 1 ? "File Conditionals" : "Form Conditionals"}
      </label>
      <div>
        {Object.entries(dataQuery).map(([key, value]) => {
          return (
            <ConditionGroup
              value={value}
              key={key}
              id={key}
              groupCount={groupCount}
              formMetadata={formMetadata}
              loadingFormMetadata={loadingFormMetadata}
              dataQuery={dataQuery}
              setDataQuery={setDataQuery}
              module={module}
              onDuplicateCheck={onDuplicateCheck}
            />
          );
        })}
        <div className={styles.addRowContainer}>
          <Button label="+ Add Group" variant="outline" onClick={addGroup} />
        </div>
      </div>
    </div>
  );
};

const ModalButtonsSection = ({ createNoConditionals, createQuery, hasAnyDuplicates }) => {
  return (
    <div className={styles.flexJustifyEnd}>
      <PrimaryButton
        text="No Conditions"
        onClick={() => createNoConditionals()}
      />
      <PrimaryButton 
        text="+ Create" 
        onClick={() => createQuery()}
        disabled={hasAnyDuplicates} 
      />
    </div>
  );
};

const ConditionGroup = ({
  id,
  value,
  groupCount,
  formMetadata,
  loadingFormMetadata,
  dataQuery,
  setDataQuery,
  module,
  onDuplicateCheck,
}) => {
  const toast = useRef(null);

  const conditionalOptions = [
    { label: "AND", value: "AND" },
    { label: "OR", value: "OR" },
  ];

  const updateGroupOperand = (operand) => {
    setDataQuery((prevState) => ({
      ...prevState,
      [id]: {
        ...(prevState[id] || { operand: 'AND', conditions: {} }),
        operand,
      },
    }));
  };

  const deleteGroup = () => {
    if (groupCount === 1) return;

    setDataQuery((prevState) => {
      const newState = { ...prevState };
      delete newState[id];
      return newState;
    });
  };

  const addCondition = () => {
    setDataQuery((prevState) => ({
      ...prevState,
      [id]: {
        ...(prevState[id] || { operand: 'AND' }),
        conditions: {
          ...(prevState[id]?.conditions || {}),
          [Guid.newGuid().toString()]: { ...defaultCondition },
        },
      },
    }));
  };

  const conditionLength = value.conditions ? Object.keys(value.conditions).length : 0;

  // Add function to check for duplicates - Yibran
  const hasDuplicateConditions = () => {
    const conditions = Object.values(value.conditions);
    const seen = new Set();
    
    return conditions.some(condition => {
      const key = `${condition.field?.guid}-${condition.operand}-${condition.value}`;
      if (seen.has(key)) return true;
      seen.add(key);
      return false;
    });
  };

  // Show toast if duplicates found - Yibran
  useEffect(() => {
    if (hasDuplicateConditions()) {
      toast.current.show({
        severity: "error",
        summary: "Duplicate Conditions",
        detail: "You have duplicate conditions with the same field, operand and value",
        life: 3000,
      });
    }
  }, [value.conditions]);

  // Pass duplicate check result to parent - Yibran
  useEffect(() => {
    if (onDuplicateCheck) {
      onDuplicateCheck(hasDuplicateConditions());
    }
  }, [value.conditions]);

  return (
    <>
      <div className={styles.conditionalContainer}>
        <Toast ref={toast} />
        <div style={{ display: "flex" }}>
          <Dropdown
            editable
            className={clsx("conditionalText", styles.conditionalDropdown)}
            options={conditionalOptions}
            value={value.operand}
            onChange={(e) => updateGroupOperand(e.value)}
          />
          <div className={styles.trashContainer} onClick={() => deleteGroup()}>
            <Image
              alt="delete"
              src={"/form-builder-studio/svg/Common/delete.svg"}
              width={30}
              height={30}
              title="delete"
              style={{ cursor: "pointer" }}
            />
          </div>
        </div>
        <div className={styles.conditionalParent}>
          {Object.entries(value.conditions).map(([key, value]) => {
            return (
              <Condition
                key={key}
                id={key}
                value={value}
                groupId={id}
                showDelete={conditionLength > 1}
                formMetadata={formMetadata}
                loadingFormMetadata={loadingFormMetadata}
                dataQuery={dataQuery}
                setDataQuery={setDataQuery}
                module={module}
              />
            );
          })}
          <div className={styles.addRowContainer}>
            <Button
              label="+ Add row"
              variant="outline"
              onClick={() => addCondition()}
            />
          </div>
        </div>
      </div>
    </>
  );
};

const Condition = ({
  id,
  groupId,
  showDelete,
  value,
  dataQuery,
  setDataQuery,
  formMetadata,
  loadingFormMetadata,
  module,
}) => {
  const { isValidComponent } = useFormConverters();
  const { getOptionsForModule, getComparisonOptionsForModule } = useModuleOptions(
    formMetadata,
    loadingFormMetadata,
    isValidComponent
  );

  const deleteCondition = () => {
    setDataQuery((prevState) => {
      const newState = { ...prevState };
      if (newState[groupId] && newState[groupId].conditions) {
        delete newState[groupId].conditions[id];
      }
      return newState;
    });
  };

  const handleSelectedFieldChange = (e) => {
    const { value } = e;

    setDataQuery((prevState) => ({
      ...prevState,
      [groupId]: {
        ...(prevState[groupId] || { operand: 'AND' }),
        conditions: {
          ...(prevState[groupId]?.conditions || {}),
          [id]: { ...defaultCondition, field: value },
        },
      },
    }));
  };

  const isFormMetadataValid = () => {
    return formMetadata?.metadata?.form && !loadingFormMetadata;
  };

  const handleOperandChange = (e) => {
    const { value } = e;

    setDataQuery((prevState) => ({
      ...prevState,
      [groupId]: {
        ...(prevState[groupId] || { operand: 'AND' }),
        conditions: {
          ...(prevState[groupId]?.conditions || {}),
          [id]: { ...prevState[groupId]?.conditions?.[id] || defaultCondition, operand: value },
        },
      },
    }));
  };

  const getCurrentComponentMetadata = () => {
    if (!isFormMetadataValid() || !value?.field?.guid) {
      return null;
    }

    return formMetadata.metadata.form[value.field.guid];
  };

  const componentOptions = getOptionsForModule(module);
  const comparisonOptions = getComparisonOptionsForModule(module, value?.field?.type);

  // Use componentMetadata for module 0 and use getDMSComponentOptions value for module 1
  const componentMetadata = getCurrentComponentMetadata();

  return (
    <>
      <div className={styles.queryRowContainer}>
        <div className={styles.rowConnector}>
          <div className={styles.arrow}>&#9660;</div>
        </div>
        <div className={styles.rowContainer}>
          <Dropdown
            className={styles.inputsDropdown}
            options={componentOptions}
            value={value.field}
            onChange={handleSelectedFieldChange}
          />
          <Dropdown
            editable
            className={styles.inputsDropdown}
            options={comparisonOptions}
            value={value.operand}
            onChange={handleOperandChange}
          />
          <ConditionalDisplay condition={componentMetadata && value?.operand}>
            <QueryBuilderInputField
              groupId={groupId}
              conditionId={id}
              componentMetadata={componentMetadata}
              dataQuery={dataQuery}
              setDataQuery={setDataQuery}
              module={module}
              value={value}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={module === 1}>
            <QueryBuilderInputField
              groupId={groupId}
              conditionId={id}
              componentMetadata={componentMetadata}
              dataQuery={dataQuery}
              setDataQuery={setDataQuery}
              module={module}
              value={value}
            />
          </ConditionalDisplay>
          {showDelete && (
            <div className={styles.trashContainer} onClick={deleteCondition}>
              <Image
                alt="delete"
                src={"/form-builder-studio/svg/Common/delete.svg"}
                width={30}
                height={30}
                title="delete"
                style={{ cursor: "pointer" }}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const QueryBuilderInputField = ({
  groupId,
  conditionId,
  componentMetadata,
  dataQuery,
  setDataQuery,
  module,
  value,
}) => {
  const { handleEventTarget } = useInputs({ initialValues: {} });

  const handleValueChange = (event) => {
    try {
      const { value } = handleEventTarget(event);

      setDataQuery((prevState) => ({
        ...prevState,
        [groupId]: {
          ...(prevState[groupId] || { operand: 'AND' }),
          conditions: {
            ...(prevState[groupId]?.conditions || {}),
            [conditionId]: {
              ...prevState[groupId]?.conditions?.[conditionId] || defaultCondition,
              value,
            },
          },
        },
      }));
    } catch (error) {
      console.error("Error setting value:", error);
    }
  };

  const componentProps = {
    value: dataQuery[groupId]?.conditions?.[conditionId]?.value || "",
    onChange: handleValueChange,
    componentMetadata: componentMetadata,
  };
  // For module 1, DMS by default it will use the QueryBuilderTextField, with dates it will use QueryBuilderDateField - Yibran
  if (module === 1) {
    if (value.field === "lastUpdated") {
      return <QueryBuilderDateField {...componentProps} />;
    }
    return <QueryBuilderTextField {...componentProps} />;
  }

  // For module 0, use the componentMetadata as is - Yibran
  return createElement(
    inputFieldMapper[componentMetadata?.type],
    componentProps
  );
};

// Below components are used to render the input fields in the QueryBuilderInputField component

const QueryBuilderTextField = ({ value, onChange }) => {
  return (
    <InputText
      value={value}
      onChange={onChange}
      className={styles.inputsDropdown}
    />
  );
};

const QueryBuilderDropdownField = ({ value, onChange, componentMetadata }) => {
  const { options } = componentMetadata;
  const fileOptions = [
    { label: "Yes", value: "Yes" },
    { label: "No", value: "No" },
  ];
  const termsAndConditionsOptions = [
    { label: "Accepted", value: "Accepted" },
    { label: "Declined", value: "Declined" },
  ];

  const isFile = componentMetadata.type === "file" ? true : false;
  const isTermsAndConditions =
    componentMetadata.type === "termsAndConditions" ? true : false;

  return (
    <Dropdown
      editable
      value={value}
      onChange={onChange}
      options={
        isFile
          ? fileOptions
          : isTermsAndConditions
            ? termsAndConditionsOptions
            : options
      }
      className={styles.inputsDropdown}
    />
  );
};

const QueryBuilderMultiSelectField = ({
  value,
  onChange,
  componentMetadata,
}) => {
  const { options, fileTypes } = componentMetadata;

  const isAdvancedFileUpload =
    componentMetadata.type === "advancedFileUpload" ? true : false;

  return (
    <MultiSelect
      value={value}
      onChange={onChange}
      options={isAdvancedFileUpload ? fileTypes : options}
      className={styles.inputsDropdown}
      style={{ maxHeight: "52px", fontWeight: "600" }}
    // This is for overriding the max-height property coming from the .p-multiselect class in globals.css
    // And matching the font-weight for the multi-select options to the other inputs
    />
  );
};

const QueryBuilderNumberField = ({ value, onChange }) => {
  const [localValue, setLocalValue] = useState(value ?? "");

  return (
    <InputNumber
      value={localValue}
      onChange={(e) => setLocalValue(e.value)}
      onBlur={onChange}
      className={styles.inputsDropdown}
      useGrouping={false}
    />
  );
};

const QueryBuilderDateField = ({ value, onChange }) => {
  return (
    <Calendar
      value={value}
      onChange={onChange}
      className={styles.inputsDropdown}
    />
  );
};

const QueryBuilderTimeField = ({ value, onChange }) => {
  const [localValue, setLocalValue] = useState(value ?? null);
  const [blurTimeout, setBlurTimeout] = useState(null);

  // Handle the blur event with a delay
  const handleBlur = () => {
    const timeoutId = setTimeout(() => {
      onChange({ target: { value: localValue } });
    }, 100); // Adjust the delay as needed
    setBlurTimeout(timeoutId);
  };

  // Handle the change event and update local state
  const handleChange = (e) => {
    setLocalValue(e.value);
    if (blurTimeout) {
      clearTimeout(blurTimeout);
      setBlurTimeout(null);
    }
  };

  return (
    <Calendar
      value={localValue}
      onChange={handleChange} // Use the handleChange function to update local state
      onBlur={handleBlur} // Use the handleBlur function to set a delayed onChange
      timeOnly
      showTime
      hourFormat="12"
      className={styles.inputsDropdown}
    />
  );
};

// Maps input field types to their corresponding QueryBuilder components, reducing the need to manually add properties to the object.
const inputFieldMapper = {
  ...["text", "textarea", "richText", "signature", "mask"].reduce(
    (acc, field) => ({ ...acc, [field]: QueryBuilderTextField }),
    {}
  ),
  ...["dropdown", "radiobutton", "file", "termsAndConditions"].reduce(
    (acc, field) => ({ ...acc, [field]: QueryBuilderDropdownField }),
    {}
  ),
  ...["multiselect", "checkbox", "advancedFileUpload"].reduce(
    (acc, field) => ({ ...acc, [field]: QueryBuilderMultiSelectField }),
    {}
  ),
  ...["number", "scale", "stars", "calculatedField"].reduce(
    (acc, field) => ({ ...acc, [field]: QueryBuilderNumberField }),
    {}
  ),
  ...["time"].reduce(
    (acc, field) => ({ ...acc, [field]: QueryBuilderTimeField }),
    {}
  ),
  ...["calendar"].reduce(
    (acc, field) => ({ ...acc, [field]: QueryBuilderDateField }),
    {}
  ),
};

export function hasMissingOrEmptyProperties(dmsQuery) {
  // Helper function to check if a field is missing or an empty string
  const isMissingOrEmpty = (obj, key) =>
    !obj.hasOwnProperty(key) || obj[key] === "";

  // Recursive function to check each operand and its conditions
  function checkConditions(conditions) {
    for (const guid in conditions) {
      if (conditions.hasOwnProperty(guid)) {
        const condition = conditions[guid];

        // Check if condition contains field, operand, and value
        if (
          isMissingOrEmpty(condition, "field") ||
          isMissingOrEmpty(condition, "operand") ||
          isMissingOrEmpty(condition, "value")
        ) {
          return true;
        }
      }
    }
    return false;
  }

  // Iterate through dmsQuery object
  for (const guid in dmsQuery) {
    if (dmsQuery.hasOwnProperty(guid)) {
      const queryPart = dmsQuery[guid];

      // Check if operand is missing or empty
      if (isMissingOrEmpty(queryPart, "operand")) {
        return true;
      }

      // Check if conditions exist and process them
      if (queryPart.hasOwnProperty("conditions")) {
        if (checkConditions(queryPart.conditions)) {
          return true;
        }
      }
    }
  }

  // If no missing or empty fields found, return false
  return false;
}
