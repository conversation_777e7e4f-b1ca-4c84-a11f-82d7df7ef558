// Libraries
import React, { useState, useContext, useRef } from "react"
import clsx from "clsx"
import { InputText } from "primereact/inputtext"
import { useRouter } from "next/router"
import { Dropdown } from "primereact/dropdown"
import { Tooltip } from "primereact/tooltip"
import { Toast } from 'primereact/toast'
// Components
import Modal from "../UI/Modal/Modal"
import PrimaryButton from "../UI/PrimaryButton/PrimaryButton"
import BreadCrumbs from "../UI/BreadCrumbs/BreadCrumbs"
import QueryBuilder from "./QueryBuilder"
import ConditionsReadOnly from "./ConditionsReadOnly"
import UserProfileContext from "../../public/UserProfileContext/UserProfileContext"
import { PageElementContainer } from "../UI/Page/PageElementContainer/PageElementContainer"
import { ResultsDashboard } from "./ResultsDashboard/ResultsDashboard"
import { useQueriesPageContext, useQueriesPagePapyrusDriveModule } from "../../public/ContextProviders/QueriesPageContextProvider"
import { useRedirectSolutionId } from "../../hooks/useRedirectSolutionId"
import { Label } from "../UI/Label/Label"
import { QueriesModules, QueryTypes } from "../../src/contants"
import { ConditionalDisplay } from "../UI/ConditionalDisplay/ConditionalDisplay"
// Styles
import styles from "./QueryDashboard.module.css"
import SecondaryButton from "../UI/SecondaryButton/SecondaryButton"

export default function QueryDashboard() {
  const router = useRouter()
  const [isVisibleBuilder, setIsVisibleBuilder] = useState(false)
  const { formDefinitionMasterFilter, queryInfo, formWorkflowDataQuery, setFormWorkflowDataQuery, papyrusDriveDataQuery, setPapyrusDriveDataQuery, solutionId } = useQueriesPageContext()
  const { routeToQueries } = useRedirectSolutionId()
  const moduleLabel = formDefinitionMasterFilter.module === 0
    ? "innovForms"
    : formDefinitionMasterFilter.module === 1
      ? "innovDocs"
      : ""

  const moduleDataQuery = formDefinitionMasterFilter.module === QueriesModules.FormWorkflow
    ? formWorkflowDataQuery
    : formDefinitionMasterFilter.module === QueriesModules.PapyrusDrive
      ? papyrusDriveDataQuery
      : {}

  const moduleSetDataQuery = formDefinitionMasterFilter.module === QueriesModules.FormWorkflow
    ? setFormWorkflowDataQuery
    : formDefinitionMasterFilter.module === QueriesModules.PapyrusDrive
      ? setPapyrusDriveDataQuery
      : null

  const routeQueries = () => {
    routeToQueries(solutionId)
  }

  return (
    <>
      <Modal header={"Query Builder"} style={{ width: '75%' }} color="#00b9ff" visible={isVisibleBuilder} onHide={() => setIsVisibleBuilder(false)}>
        <QueryBuilder dataQuery={moduleDataQuery} setDataQuery={moduleSetDataQuery} setIsVisibleBuilder={setIsVisibleBuilder} />
      </Modal>
      <div className={styles.flexRow}>
        <div style={{ display: "flex", alignItems: "center", width: "-webkit-fill-available" }}>
          <Tooltip target=".returnIcon" />
          <div
            className={clsx("pi pi-chevron-left", styles.returnIcon, "returnIcon")}
            onClick={routeQueries}
            data-pr-tooltip="Back"
          />
          <BreadCrumbs
            title={"Queries"}
            breadcrumbItems={[
              { label: "Queries" },
              ...(moduleLabel ? [{ label: moduleLabel }] : []),
              ...(queryInfo?.name ? [{ label: queryInfo.name }] : [])
            ]}
            leftMargin="0px"
          />
        </div>
        <QueriesButtons setIsVisibleBuilder={setIsVisibleBuilder} routeQueries={routeQueries} />
      </div>
      <ConditionsReadOnly dataQuery={moduleDataQuery} />
      <PageElementContainer height="-webkit-fill-available" maxHeight="100%" overflow="scroll">
        <ResultsDashboard />
      </PageElementContainer>
    </>
  )
}

const QueriesButtons = ({ setIsVisibleBuilder, routeQueries }) => {
  const [isVisibleSaveQueryModal, setIsVisibleSaveQueryModal] = useState(false)
  const [isSaveAsMode, setIsSaveAsMode] = useState(false)
  const userProfile = useContext(UserProfileContext)
  const {
    postSaveQuery,
    updateSavedQuery,
    queryInfo,
    setQueryInfo,
    checkIfUserBuiltQuery,
    formDefinitionMasterFilter,
    loading
  } = useQueriesPageContext()
  const { papyrusDriveState } = useQueriesPagePapyrusDriveModule()
  const isForUpdate = queryInfo?.id && !isSaveAsMode
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)
  const toast = useRef(null)

  const isButtonDisabled = formDefinitionMasterFilter.module === 0
    ? !formDefinitionMasterFilter.department || !formDefinitionMasterFilter.formDefinitionMaster
    : formDefinitionMasterFilter.module === 1
      ? !papyrusDriveState.department?.length || !papyrusDriveState.document?.length
      : true;

  const handleButtonSaveOrUpdate = async (e) => {

    if (isSaving) return
    setIsSaving(true)

    try {
      if (isForUpdate) {
        await updateSavedQuery(e)
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Query updated successfully'
        })
      } else {
        await postSaveQuery(e)
        const detail = isSaveAsMode ? 'Query saved as new copy successfully' : 'Query saved successfully'
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: detail
        })
      }

      // Reset Save As mode after successful save
      setIsSaveAsMode(false)
      
      setTimeout(() => {
        routeQueries()
      }, 1000)
    } catch (error) {
      const operation = isForUpdate ? 'updating' : 'saving'
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: `An error occurred while ${operation} the query`
      })
      console.error(`Error ${operation} query:`, error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleSaveAs = () => {
    // Set Save As mode - keep original name so user can modify it
    setIsSaveAsMode(true)
    
    // Open the save modal
    setIsVisibleSaveQueryModal(true)
  }

  return (
    <>
      <Toast ref={toast} />
      <Modal header={"Save Query"} style={{ width: '40rem' }} color="#00b9ff" visible={isVisibleSaveQueryModal} onHide={() => {
        setIsVisibleSaveQueryModal(false)
        setIsSaveAsMode(false)
      }}>
        <SaveQuery queryInfo={queryInfo} setQueryInfo={setQueryInfo} onButtonClick={handleButtonSaveOrUpdate} loading={loading} />
      </Modal>
      <div className={styles.queriesContainer}>
        <div className={styles.queriesButtonContainer}>
          <SecondaryButton
            text={"+ Add Criteria"}
            width="200"
            onClick={() => setIsVisibleBuilder(true)}
            disabled={isButtonDisabled}
          />
          <ConditionalDisplay condition={router.pathname.includes("/create")}>
            <PrimaryButton
              text="Save Query"
              width={200}
              disabled={checkIfUserBuiltQuery()}
              onClick={() => setIsVisibleSaveQueryModal(true)}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={router.pathname.includes("/view") && userProfile.displayName === queryInfo.createdByDisplayName}>
            <PrimaryButton
              text="Update Query"
              width={200}
              disabled={checkIfUserBuiltQuery()}
              onClick={() => setIsVisibleSaveQueryModal(true)}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={router.pathname.includes("/view") && queryInfo?.id}>
            <PrimaryButton
              text="Save As"
              width={200}
              disabled={checkIfUserBuiltQuery()}
              onClick={handleSaveAs}
            />
          </ConditionalDisplay>
        </div>
      </div>
    </>
  )
}

const SaveQuery = ({ queryInfo, setQueryInfo, onButtonClick, loading }) => {

  const getQueryTypes = () => {
    return Object.keys(QueryTypes).map((key) => {
      return { label: key, value: QueryTypes[key] }
    })
  }

  const queryTypes = getQueryTypes() ?? []

  return (
    <div className={styles.saveQueryContainer}>
      <div className={styles.inputContainer}>
        <Label label="Query Name" asterisk />
        <InputText value={queryInfo.name} onChange={(e) => setQueryInfo({ ...queryInfo, name: e.target.value })} />
      </div>
      <div className={styles.inputContainer}>
        <Label label="Query Type" asterisk />
        <Dropdown options={queryTypes} value={queryInfo.queryType} onChange={(e) => setQueryInfo({ ...queryInfo, queryType: e.target.value })} />
      </div>
      <div className={styles.inputContainer}>
        <PrimaryButton
          text="Save Query"
          onClick={onButtonClick}
          buttonstyle={{ width: '100%' }}
          disabled={loading || !queryInfo?.name?.trim()}
          loading={loading}
        />
      </div>
    </div>
  )
}
