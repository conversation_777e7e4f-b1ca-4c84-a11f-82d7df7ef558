// This should be in sync with the database enum values
// We can create a context for this and use it in the application after fetching from database

export const SolutionIds = {
    Papyrus: 1,
    PerformanceReview: 2,
    OnboardingProcess: 3,
    SupplierQualification: 4,
    ProjectRequisition: 5,
    PurchaseRequisition: 6,
    AccessRequest: 7,
    JobRequisition: 8
}

export const ReverseSolutions = Object.entries(SolutionIds)
    .reduce((acc, [key, value]) => {
        acc[value] = key;
        return acc;
    }, {})

// Below value should be in sync with the database enum values
export const SubmissionStages = {
    Submitted: 1,
    Inprogress: 2,
    Approved: 3,
    Rejected: 4,
    RevisionRequired: 5,
    Completed: 6,
    Processed: 7,
    Cancelled: 8,
    OnHold: 9,
    Recall: 10,
    Draft: 11,
    Resume: 12,
    Restarted: 13,
    Demote: 14
}

// Reverse the SubmissionStage 
export const ReverseSubmissionStages = Object.entries(SubmissionStages)
    .reduce((acc, [key, value]) => {
        acc[value] = key;
        return acc;
    }, {})

export const QueryTypes = {
    Private: 1,
    Public: 2
}

export const ReverseQueryTypes = Object.entries(QueryTypes)
    .reduce((acc, [key, value]) => {
        acc[value] = key;
        return acc;
    }, {})

export const LovMasters = {
    Root: 1,
    Department: 2,
    SubDepartment: 3,
    Team: 4
}

export const ReverseLovMasters = Object.entries(LovMasters)
    .reduce((acc, [key, value]) => {
        acc[value] = key;
        return acc;
    }, {})

export const QueriesModules = {
    FormWorkflow: 0,
    PapyrusDrive: 1
}

export const dmsStatuses = {
    // "0": "Disabled",
    "1": "In Progress",
    "2": "Approved",
    "3": "Rejected",
    "4": "Submitted"
}

export const getDmsSubmissionStage = (routeStatus) => {
    if (routeStatus === undefined || routeStatus === null) {
        return "-";
    }

    return dmsStatuses[routeStatus] || "Unknown Status";
}