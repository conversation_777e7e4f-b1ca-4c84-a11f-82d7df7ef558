import Flex from '../../../UI/Layout/Flex/Flex'
import Header from '../../../UI/Header/Header'
import ApprovalNodes from '../../../UI/ApprovalNodes/ApprovalNodes'
import ChildFormNodes from '../../../UI/ApprovalNodes/ChildFormNodes'

import HorizontalScroll from '../../../UI/HorizontalScroll/HorizontalScroll'
import UserProfileContext from '../../../../public/UserProfileContext/UserProfileContext'
import { useContext, useEffect, useState, useRef } from 'react'
import { PageElementContainer } from '../../../UI/Page/PageElementContainer/PageElementContainer'
import { useOnBoardingContextProvider } from '../../../../public/ContextProviders/OnBoardingContextProvider'
import { ConditionalDisplay } from '../../../UI/ConditionalDisplay/ConditionalDisplay'
import { useRouter } from 'next/router'
import Image from 'next/image'
import { ProgressSpinner } from 'primereact/progressspinner'
import header from '../../../../svg/Header_chevron.svg'
import lineItem from '../../../../svg/LineItem_chevron.svg'
import { getUserProfileByEmail } from '../../../../api/apiCalls'

import styles from './ApproverStatus.module.css'
import overviewStyles from './OverviewStatus.module.css'

// Employee Onboarding Card Component
export const EmployeeOnboardingCard = ({
  title,
  name,
  phone,
  email,
  supervisor,
  supervisorPhone,
  supervisorEmail,
  department,
  onClick
}) => {
  const [isHovered, setIsHovered] = useState(false)
  
  return (
    <div
      onClick={onClick}
      style={{
        boxShadow: isHovered ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 8px rgba(0,0,0,0.1)',
        transform: isHovered ? 'translateY(-2px)' : 'translateY(0)'
      }}
      className={overviewStyles.onboardingCard}
      onMouseEnter={() => {
        setIsHovered(true)
      }}
      onMouseLeave={() => {
        setIsHovered(false)
      }}
    >
      {/* Header with name and profile picture placeholder */}
      <div className={overviewStyles.header}>
        <div className={overviewStyles.headerNameIcon}>{name?.charAt(0) || 'U'}</div>
        <div>
          <div className={overviewStyles.headerNameUser}>{name || 'Unknown User'}</div>
          <div className={overviewStyles.headerTitle}>{title || 'Employee Onboarding'}</div>
        </div>
      </div>

      {/* Contact Details */}
      <div className={overviewStyles.containerMarginBottom}>
        <div className={overviewStyles.detailsSection}>Contact Details</div>
        <div className={`${overviewStyles.detailsSectionValue} ${overviewStyles.detailsSectionValueMarginBottom}`}>Phone: {phone}</div>
        <div className={overviewStyles.detailsSectionValue}>Email: {email}</div>
      </div>

      {/* Supervisor Details */}
      <div className={overviewStyles.containerMarginBottom}>
        <div className={overviewStyles.detailsSection}>Supervisor Details</div>
        <div className={`${overviewStyles.detailsSectionValue} ${overviewStyles.detailsSectionValueMarginBottom}`}>
          Supervisor: {supervisor}
        </div>
        <div className={`${overviewStyles.detailsSectionValue} ${overviewStyles.detailsSectionValueMarginBottom}`}>
          Phone: {supervisorPhone}
        </div>
        <div className={overviewStyles.detailsSectionValue}>Email: {supervisorEmail}</div>
      </div>

      {/* Department */}
      <div>
        <div className={overviewStyles.detailsSection}>Department</div>
        <div className={overviewStyles.detailsSectionValue}>{department || 'N/A'}</div>
      </div>
    </div>
  )
}

export const ChildForms = ({
  childForms,
  childFormSubmissions,
  handleFormApprovalPageChange,
  initialFormSubmission,
  initialFormSubmissionData,
  returnToRequestOverview
}) => {
  const [userProfile, setUserProfile] = useState(null)
  
  // Extract employee data from form submission data
  const extractedEmployeeData = extractEmployeeDataFromFormData(initialFormSubmissionData)
  
  const handleClick = (formSubmission, formSubmissionId, formMetadata, formSubmissionData, formConditions) => {
    handleFormApprovalPageChange(formSubmission, formSubmissionId, formMetadata, formSubmissionData, formConditions)
  }

  // Fetch user profile data including manager information
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (extractedEmployeeData.email && extractedEmployeeData.email !== '<EMAIL>') {
        try {
          const profileData = await getUserProfileByEmail(extractedEmployeeData.email)
          setUserProfile(profileData.data)
        } catch (error) {
          console.error('Error fetching user profile:', error)
        }
      }
    }

    fetchUserProfile()
  }, [extractedEmployeeData.email])

  // Filter out submissions that don't have formSubmission data
  const allSubmissions = childFormSubmissions?.filter((submission) => submission && submission.formSubmission) || []

  if (allSubmissions.length === 0) {
    return null
  }

  const LAYOUT_DIMENSIONS = {
    onboardingCardWidth: 340,
    cardGap: 20,
    lineItemCardWidth: 240
  }

  return (
    <div style={{ width: '100%', marginTop: '20px' }}>
      <HorizontalScroll>
        <div
          style={{
            width: `${
              LAYOUT_DIMENSIONS.onboardingCardWidth + 
              LAYOUT_DIMENSIONS.cardGap + 
              allSubmissions.length * 
              LAYOUT_DIMENSIONS.lineItemCardWidth
            }px`,
            display: 'flex',
            alignItems: 'center'
          }}
          className={overviewStyles.onboardingCardContainer}
        >
          {/* Always show the Employee Onboarding node */}
          <div style={{ flexShrink: 0 }}>
            <EmployeeOnboardingCard
              title={initialFormSubmission?.formDefinition?.name}
              name={extractedEmployeeData.fullLegalName}
              date={initialFormSubmission?.submittedAtUtc}
              groupName={initialFormSubmission?.currentApproverGroup}
              phone={extractedEmployeeData.phone}
              email={extractedEmployeeData.email}
              supervisor={userProfile?.manager?.displayName || 'N/A'}
              supervisorPhone={userProfile?.manager?.phone || '************'}
              supervisorEmail={userProfile?.manager?.email || '<EMAIL>'}
              department={initialFormSubmission?.formDefinition?.formDefinitionMaster?.department?.name || 'N/A'}
              onClick={() => returnToRequestOverview()}
            />
          </div>

          {/* All Line Items */}
          {allSubmissions.map((childFormSubmissions, index) => {
            const correspondingChildFormIndex = childFormSubmissions?.formSubmission?.formDefinitionId
              ? childForms?.findIndex((form) => form?.formDefinitionId === childFormSubmissions?.formSubmission?.formDefinitionId)
              : index

            // Get the computed status for this individual submission
            const computedStatus = getChildFormStatus(childFormSubmissions)

            return (
              <div key={`child-form-${index}`} style={{ flexShrink: 0 }}>
                <ChildFormNodes
                  isFinalized={childFormSubmissions?.formSubmission?.isFinalized}
                  status={childFormSubmissions?.formSubmission?.formTimelines}
                  childFormSubmissions={childFormSubmissions?.formSubmission}
                  name={
                    childFormSubmissions?.formSubmission?.currentApprover
                      ? childFormSubmissions?.formSubmission?.currentApproverFullLegalName
                      : ''
                  }
                  formName={childFormSubmissions?.formSubmission?.formDefinition?.name}
                  groupName={childFormSubmissions?.currentFormGroup?.groupName}
                  lastUpdatedDate={childFormSubmissions?.formSubmission?.lastUpdatedAtUtc}
                  computedStatus={computedStatus}
                  onClick={() =>
                    handleClick(
                      childFormSubmissions.formSubmission,
                      childFormSubmissions.formSubmission.id,
                      correspondingChildFormIndex >= 0 ? childForms[correspondingChildFormIndex]?.formDefinitionForm : null,
                      childFormSubmissions.formSubmissionData,
                      correspondingChildFormIndex >= 0 ? childForms[correspondingChildFormIndex]?.formDefinitionConditions : null
                    )
                  }
                />
              </div>
            )
          })}
        </div>
      </HorizontalScroll>
    </div>
  )
}

export const ApproverTimeLine = ({ events, formSubmission }) => {
  const filteredEvents = events?.filter((event) => event.status !== 'Submitted')
  const currentReviewIndex = filteredEvents?.findIndex((event) => event.status === 'InReview')

  return (
    <>
      {filteredEvents?.map((event, index) => (
        <Flex direction={'column'} className={styles.nodeSpacing} key={index}>
          <ApprovalNodes
            status={event.status}
            stageLabel={event.stageLabel}
            name={event.name}
            userName={event.userName}
            date={event.date}
            jobTitle={event.jobTitle}
            department={event.department}
            groupName={event.groupName}
            transactionStatus={event.transactionStatus}
            isLastNode={event.isLastNode}
            formSubmission={formSubmission}
            isCurrentReview={index === currentReviewIndex}
          />
        </Flex>
      ))}
    </>
  )
}

export const OverviewStatus = ({ formSubmission, handleFormApprovalPageChange, initialFormSubmission, initialFormSubmissionData, returnToRequestOverview }) => {
  const userProfile = useContext(UserProfileContext)
  const { childFormSubmissions, childForms } = useOnBoardingContextProvider()

  const filteredChildFormSubmissions = filterChildFormSubmissions(userProfile, childFormSubmissions)

  // The filteredChildForms variable must come after filteredChildFormSubmissions as it's depedendent on it to work. - Alex
  const filteredChildForms = filterChildFormDefinitions(userProfile, filteredChildFormSubmissions, childForms)
  const [loading, setLoading] = useState(true)
  const [events, setEvents] = useState([])
  const router = useRouter()

  useEffect(() => {
    setEvents(getEvent(formSubmission?.formTimelines, formSubmission?.currentApprover, formSubmission?.formTransactions))

    if (
      // This condition deactivates the ProgressSpinner component once true. - Alex
      Array.isArray(childFormSubmissions) &&
      childFormSubmissions.length > 0
    ) {
      setLoading(false)
    }
  }, [formSubmission, childFormSubmissions])

  const getEvent = (timeLineData, currentStatus, formTransactionData) => {
    let rejectionOccurred = false
    let usedTransactions = new Set()

    return timeLineData?.map((data, index, array) => {
      const transactionForThisData = formTransactionData?.find(
        (transaction) => transaction.approver === data.user?.email && !usedTransactions.has(transaction.transactionId)
      )

      if (transactionForThisData) {
        usedTransactions.add(transactionForThisData.transactionId)
      }

      const currentData = {
        id: data?.id,
        name: data?.user?.displayName,
        userName: data?.user?.email,
        status: data?.statusText,
        stageLabel: data?.stageLabel,
        date: data?.lastUpdatedDateTime,
        active: data?.user?.email === currentStatus,
        jobTitle: data?.user?.jobTitle,
        department: data?.user?.departmentName,
        isLastNode: index === array.length - 1,
        groupName: data?.userGroup?.groupName,
        transactionStatus: transactionForThisData ? transactionForThisData.statusText : null
      }

      if (rejectionOccurred) {
        currentData.status = 'Cancelled'
      }

      if (data.statusText === 'Rejected') {
        rejectionOccurred = true
      }

      return currentData
    })
  }

  const currentUserStageLabel = formSubmission?.formTimelines
    ?.slice(1)
    .find((item) => item?.user?.displayName === formSubmission?.currentApproverFullLegalName)?.stageLabel

  return (
    <>
      {loading ? (
        <ProgressSpinner style={{ width: '100px', height: '100px', color: '#00b9ff' }} strokeWidth="2" animationDuration=".5s" />
      ) : (
        <PageElementContainer marginTop="0px">
          <ConditionalDisplay condition={formSubmission?.id !== router.query.fid}>
            <div>
              <SubmissionIdSubText
                isHeaderPage={initialFormSubmission?.formSubmissionId === formSubmission?.formSubmissionId}
                submissionId={formSubmission?.formSubmissionId}
              />
            </div>
            <HorizontalScroll>
              <Flex style={{ gap: '40px', justifyContent: 'flex-start', width: '100%', paddingLeft: '4%' }}>
                <Flex className={styles.nodeSpacing}>
                  <ApprovalNodes
                    stageLabel={initialFormSubmission?.formDefinition?.name}
                    name={initialFormSubmission?.userFullLegalName}
                    date={initialFormSubmission?.submittedAtUtc}
                    groupName={initialFormSubmission?.currentApproverGroup}
                    onClick={() => returnToRequestOverview()}
                  />
                </Flex>
                <ApproverTimeLine events={events} formSubmission={formSubmission} />
              </Flex>
            </HorizontalScroll>

            {/* Line Item Details */}
            <div className={overviewStyles.lineItemDetails}>
              <ItemHeader header="Line ID:" value={formSubmission?.formSubmissionId || 'N/A'} width="18%" />
              <ItemHeader
                header="Department:"
                value={formSubmission?.formDefinition?.formDefinitionMaster?.department?.name || 'N/A'}
                width="18%"
              />
              <ItemHeader header="Title:" value={formSubmission?.formDefinition?.name || 'N/A'} width="22%" />
              <ItemHeader header="Current Status:" value={formSubmission?.statusName || 'InProgress'} width="18%" />
              <ItemHeader
                header="Current Stage:"
                value={currentUserStageLabel || formSubmission?.formDefinition?.name || 'N/A'}
                width="22%"
              />
            </div>
          </ConditionalDisplay>
          <ConditionalDisplay condition={formSubmission?.id === router.query.fid}>
            <ChildForms
              childForms={filteredChildForms}
              childFormSubmissions={filteredChildFormSubmissions}
              handleFormApprovalPageChange={handleFormApprovalPageChange}
              initialFormSubmission={initialFormSubmission}
              initialFormSubmissionData={initialFormSubmissionData}
              returnToRequestOverview={returnToRequestOverview}
            />
          </ConditionalDisplay>
        </PageElementContainer>
      )}
    </>
  )
}

export const ItemHeader = ({ header, value, width }) => {
  return (
    <Flex style={{ width: `${width}` }}>
      <Header size={4} className={styles.itemHeader}>
        {header}
      </Header>
      <span className={styles.itemResult}>{value}</span>
    </Flex>
  )
}

// This component is for displaying whether the currently viewed form is a header page or line item.
// This was created based on Guna's feedback. - Alex
export const SubmissionIdSubText = ({ isHeaderPage, submissionId }) => {
  return (
    <div className={styles.SubmissionIdSubText} style={{ marginBottom: '20px', display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Image src={isHeaderPage ? header : lineItem} alt={isHeaderPage ? 'Header icon' : 'Line item icon'} width={20} height={20} />
      <span>{isHeaderPage ? 'Header' : 'Line Item'}</span>
      {isHeaderPage && submissionId && <span style={{ marginLeft: '8px', color: '#666' }}>- Submission ID: {submissionId}</span>}
    </div>
  )
}

export const filterChildFormSubmissions = (userProfile, childFormSubmissions) => {
  if (checkIfAdminOrSubmitter(userProfile, childFormSubmissions)) return childFormSubmissions

  return childFormSubmissions.filter((childFormSub) => {
    // This if check will return child forms where the signed-in user is part of the workflow or the current group. - Alex
    if (
      isUserInFormTimelines(childFormSub, userProfile?.displayName) ||
      isUserInGroupApproval(childFormSub?.allGroups, userProfile?.email)
    ) {
      return childFormSub
    }
  })
}

export const filterChildFormDefinitions = (userProfile, filteredChildFormSubmissions, childForms) => {
  if (checkIfAdminOrSubmitter(userProfile, filteredChildFormSubmissions)) return childForms

  // This if check will return child forms where the signed-in user is part of the workflow.
  // It's dependent on the filtered child form submissions because I couldn't find another way to filter the child forms
  // with how they are currently structured. - Alex
  return childForms.filter((childForm) =>
    filteredChildFormSubmissions.some((submission) => submission?.formSubmission?.formDefinitionId === childForm?.formDefinitionId)
  )
}

export const checkIfAdminOrSubmitter = (userProfile, childFormSubmissions) => {
  const isSubmitter = childFormSubmissions[0]?.formSubmission?.userFullLegalName === userProfile?.displayName
  return userProfile?.role?.isAdmin || isSubmitter // Admins & submitters (managers) should see all child forms. - Alex
}

const isUserInFormTimelines = (formSubmission, signedInUserName) => {
  if (!formSubmission.formSubmission || !formSubmission.formSubmission.formTimelines) {
    return false
  }

  return formSubmission.formSubmission.formTimelines.some((timeline) => {
    if (!timeline.user || !timeline.user.displayName) {
      return false
    }

    return timeline.user.displayName === signedInUserName
  })
}

const isUserInGroupApproval = (formSubmissionGroups, userEmail) => {
  if (!formSubmissionGroups || !userEmail) return false

  return formSubmissionGroups.some((group) => {
    if (!group || !Array.isArray(group)) return false

    return group.some((groupObj) => groupObj !== null && groupObj?.users?.some((user) => user.email === userEmail))
  })
}

// Helper function to determine the status of a child form submission
const getChildFormStatus = (childFormSubmission) => {
  const formSubmission = childFormSubmission?.formSubmission
  const status = formSubmission?.formTimelines
  const isFinalized = formSubmission?.isFinalized
  const statusName = formSubmission?.statusName

  // Check finalized status first
  if (isFinalized) {
    if (statusName === 'Rejected') {
      return 'Rejected'
    } else if (statusName === 'Approved') {
      return 'Approved'
    }
  }

  // Check for specific status names for non-finalized items
  if (statusName === 'OnHold') {
    return 'OnHold'
  } else if (statusName === 'Cancelled') {
    return 'Cancelled'
  } else if (statusName === 'Recalled') {
    return 'Recalled'
  }

  // Check timeline status for non-finalized items
  if (status && status.length > 0) {
    if (status.some((s) => s.statusText === 'Rejected')) {
      return 'Rejected'
    } else if (status.every((s) => s.statusText === 'Approved')) {
      return 'Approved'
    } else if (status.some((s) => s.statusText === 'InReview')) {
      return 'InProgress'
    }
  }

  // If no significant progress has been made, consider it Draft
  return 'Draft'
}

// Helper function to extract employee data from employee lookup component data
// This function works as expected as long as there's only one employee lookup component on the form
const extractEmployeeDataFromFormData = (formSubmissionData) => {
  const defaultData = {
    fullLegalName: 'Unknown Employee',
    email: '<EMAIL>',
    phone: '************'
  }

  if (!formSubmissionData || typeof formSubmissionData !== 'object') {
    return defaultData
  }

  // Find all keys that start with "employeeLookup_"
  const employeeLookupKeys = Object.keys(formSubmissionData).filter(key => 
    key.startsWith('employeeLookup_')
  )

  // Validation: warn if multiple employee lookup components found
  if (employeeLookupKeys.length > 1) {
    console.warn(`Multiple employee lookup components found (${employeeLookupKeys.length}). Using the first one.`)
  }

  // If no employee lookup found, return default data
  if (employeeLookupKeys.length === 0) {
    return defaultData
  }

  // Get the first employee lookup data
  const employeeData = formSubmissionData[employeeLookupKeys[0]]
  
  // Extract and validate employee data
  const fullLegalName = employeeData?.fullLegalName && typeof employeeData.fullLegalName === 'string' && employeeData.fullLegalName.trim()
    ? employeeData.fullLegalName.trim()
    : defaultData.fullLegalName

  const email = employeeData?.email && typeof employeeData.email === 'string' && employeeData.email.trim()
    ? employeeData.email.trim()
    : defaultData.email

  const phone = employeeData?.phone && typeof employeeData.phone === 'string' && employeeData.phone.trim()
    ? employeeData.phone.trim()
    : defaultData.phone

  return {
    fullLegalName,
    email,
    phone
  }
}
