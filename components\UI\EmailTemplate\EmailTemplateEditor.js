import { useState, useEffect } from 'react'
import clsx from 'clsx'
import { useAccount, useMsal } from '@azure/msal-react'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import { getAccessTokenForScopeSilent } from '../../../src/GetAccessTokenForScopeSilent'
import Button from '../Button/Button'
import SelectInput from '../Input/SelectInput/SelectInput'
import TextInput from '../Input/TextInput/TextInput'
import TextareaInput from '../Input/TextareaInput/TextareaInput'
import Editor from '../../LexicalEditor/LexicalEditor'
import styles from '../../../pages/LeadGeneration/index.module.css'
import tStyle from '../../../pages/template/template.module.css'
import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html'
import { $getRoot, $insertNodes } from 'lexical'
import emailTemplateEditorStyles from './EmailTemplateEditor.module.css'
import { Label } from '../Label/Label'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

const contentTypeOptions = [
  { label: 'Rich Text', value: 0 },
  { label: 'HTML', value: 1 }
]

const defaultNewTemplate = {
  id: 'new',
  name: '',
  subject: '',
  content: '',
  contentHtml: '',
  type: 0
}

export function EmailTemplateEditor({
  selectedTemplateId,
  onTemplateSelect,
  emailVariables = [],
  flowDirection = 'horizontal' // 'horizontal' or 'vertical'
}) {
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const [templateLoading, setTemplateLoading] = useState(false)
  const [existingTemplates, setExistingTemplates] = useState([])
  const [templates, setTemplates] = useState({})
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [loading, setLoading] = useState(false)
  const [testEmailLoading, setTestEmailLoading] = useState(false)

  useEffect(() => {
    if (account) {
      loadExistingTemplates()
    }
  }, [account])

  useEffect(() => {
    if (selectedTemplateId === null) {
      setSelectedTemplate('new')
    } else if (selectedTemplateId && templates[selectedTemplateId] && selectedTemplate !== selectedTemplateId && !templateLoading) {
      setSelectedTemplate(selectedTemplateId)
    }
  }, [selectedTemplateId, templates, templateLoading, selectedTemplate])

  const loadExistingTemplates = async () => {
    try {
      setTemplateLoading(true)
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
      const response = await fetch(`${api}EmailTemplates`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })

      if (response.ok) {
        const data = await response.json()

        const templatesData = {
          new: { ...defaultNewTemplate },
          ...data.reduce((acc, t) => {
            acc[t.id.toString()] = t
            return acc
          }, {})
        }

        setTemplates(templatesData)

        setExistingTemplates(
          Object.values(templatesData).map((template) => ({
            label: template.id === 'new' ? 'Create New Template' : template.name,
            value: template.id.toString()
          }))
        )
      }
    } catch (err) {
      setExistingTemplates([{ label: 'Create New Template', value: 'new' }])
      setTemplates({ new: { ...defaultNewTemplate } })
    } finally {
      setTemplateLoading(false)
    }
  }

  const onTemplateUpdate = ({ guid, name, value }) => {
    setTemplates((prevTemplates) => ({
      ...prevTemplates,
      [guid]: {
        ...prevTemplates[guid],
        [name]: value
      }
    }))
  }

  const upsertTemplateAsync = async () => {
    if (!selectedTemplate || !templates[selectedTemplate]) {
      console.error('No template selected for update')
      return
    }

    try {
      setLoading(true)

      const currentTemplate = templates[selectedTemplate]
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)
      const isNewTemplate = selectedTemplate === 'new'

      if (isNewTemplate) {
        currentTemplate.id = null
      }

      const response = await fetch(`${api}EmailTemplates`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(currentTemplate)
      })

      if (!response.ok) {
        throw new Error(`Failed to save template: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      if (isNewTemplate && result) {
        const newTemplateId = result.id.toString()

        setTemplates((prevTemplates) => ({
          ...prevTemplates,
          [newTemplateId]: result,
          new: { ...defaultNewTemplate } // Reset using default object
        }))

        setExistingTemplates((prevOptions) => [
          prevOptions[0],
          {
            label: result.name,
            value: newTemplateId
          },
          ...prevOptions.slice(1)
        ])

        setSelectedTemplate(newTemplateId)
        if (onTemplateSelect) {
          onTemplateSelect(newTemplateId)
        }
      } else {
        if (onTemplateSelect && selectedTemplate) {
          onTemplateSelect(selectedTemplate)
        }
      }
    } catch (err) {
      console.error(`Error saving template:`, err)
    } finally {
      setLoading(false)
    }
  }

  const sendTestEmail = async () => {
    if (!selectedTemplate || !templates[selectedTemplate]) {
      console.error('No template selected or email address provided for test')
      return
    }

    try {
      setTestEmailLoading(true)
      const currentTemplate = templates[selectedTemplate]
      const accessToken = await getAccessTokenForScopeSilent(formBuilderApiRequest)

      const testEmailData = {
        ToAddresses: ['<EMAIL>'],
        CcAddresses: [],
        BccAddresses: [],
        Subject: `[TEST] ${currentTemplate.subject}`,
        Body: currentTemplate.content,
        BodyHtml: currentTemplate.content
      }

      const response = await fetch(`${api}Email/Send`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testEmailData)
      })

      if (!response.ok) {
        throw new Error(`Failed to send test email: ${response.status} ${response.statusText}`)
      }
    } catch (err) {
      console.error('Error sending test email:', err)
    } finally {
      setTestEmailLoading(false)
    }
  }

  // Dynamic styles based on flow direction
  const isVertical = flowDirection === 'vertical'
  const containerStyle = {
    display: 'flex',
    flexDirection: isVertical ? 'column' : 'row',
    gap: '10px'
  }

  const templateSectionStyle = {
    width: isVertical ? '100%' : '50%',
    display: 'flex',
    flexDirection: 'column'
  }

  const previewSectionStyle = {
    width: isVertical ? '100%' : '50%',
    opacity: !selectedTemplate && !templateLoading ? 0.5 : 1
  }

  return (
    <div style={{ paddingTop: '5px', position: 'relative' }}>
      <div
        style={{
          opacity: templateLoading ? 0.5 : 1,
          pointerEvents: templateLoading ? 'none' : 'auto'
        }}
      >
        <div style={{ paddingTop: '5px' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
            <Label label="Select Email Template" />
            <SelectInput
              loading={templateLoading}
              value={selectedTemplate}
              options={existingTemplates}
              onChange={(e) => {
                setSelectedTemplate(e.target.value)
                if (onTemplateSelect) {
                  onTemplateSelect(e.target.value)
                }
              }}
              theme="metronic"
              style={{ width: '100%', marginBottom: '20px' }}
            />
          </div>

          <div style={containerStyle}>
            <div className={clsx('p-0', styles.gridCard)} style={templateSectionStyle}>
              <div className={styles.formTitle}>Template Information</div>
              <EmailTemplate
                template={templates[selectedTemplate]}
                onTemplateUpdate={onTemplateUpdate}
                disabled={!selectedTemplate && !templateLoading}
                emailVariables={emailVariables}
              />
              <div
                style={{
                  padding: '10px',
                  paddingTop: '0',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '10px'
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                  <Button
                    label="Send Test Email"
                    onClick={sendTestEmail}
                    disabled={testEmailLoading || !selectedTemplate || selectedTemplate === 'new'}
                    theme="metronic"
                    variant="outline"
                    loading={testEmailLoading}
                    style={{
                      opacity: !selectedTemplate || selectedTemplate === 'new' ? 0.5 : 1,
                      pointerEvents: !selectedTemplate || selectedTemplate === 'new' ? 'none' : 'auto'
                    }}
                  />
                </div>
                <Button
                  label={selectedTemplate !== 'new' ? 'Update Template' : 'Create Template'}
                  onClick={upsertTemplateAsync}
                  disabled={loading}
                  theme="metronic"
                  variant="fill"
                  loading={loading}
                  style={{
                    opacity: !selectedTemplate ? 0.5 : 1,
                    pointerEvents: !selectedTemplate ? 'none' : 'auto'
                  }}
                />
              </div>
            </div>
            <div className={clsx('p-0', styles.gridCard)} style={previewSectionStyle}>
              <div className={styles.formTitle}>Preview</div>
              <div className={tStyle.previewBox} style={{ padding: '20px' }}>
                <EmailTemplatePreview content={templates[selectedTemplate]?.content || ''} type={templates[selectedTemplate]?.type || 0} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const EmailTemplate = ({ template, onTemplateUpdate, disabled, emailVariables = [] }) => {
  const [lexicalEditor, setLexicalEditor] = useState(null)

  useEffect(() => {
    if (lexicalEditor && template?.type === 0) {
      lexicalEditor.update(() => {
        const parser = new DOMParser()
        const dom = parser.parseFromString(template.content, 'text/html')

        const nodes = $generateNodesFromDOM(lexicalEditor, dom)

        $getRoot().clear()
        $getRoot().select()
        $insertNodes(nodes)
      })
    }
  }, [lexicalEditor, template?.content, template?.id])

  const handleLexicalChange = (value) => {
    if (!lexicalEditor) return
    let htmlString = ''
    lexicalEditor.read(() => {
      htmlString = $generateHtmlFromNodes(lexicalEditor)
    })
    onTemplateUpdate({ guid: template?.id, name: 'content', value: htmlString })
  }

  return (
    <div
      className={emailTemplateEditorStyles.emailTemplateContainer}
      style={{
        padding: '10px',
        display: 'flex',
        flexDirection: 'column',
        opacity: disabled ? 0.5 : 1,
        pointerEvents: disabled ? 'none' : 'auto'
      }}
    >
      <div className="flex" style={{ gap: '10px', marginBottom: '10px' }}>
        <div style={{ flex: 1 }}>
          <TextInput
            label="Template Name"
            value={template?.name ?? ''}
            onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'name', value: e.target.value })}
            theme="metronic"
            style={{ width: '100%' }}
            required={true}
          />
        </div>
        <div style={{ flex: 1 }}>
          <SelectInput
            label="Content Type"
            value={template?.type}
            options={contentTypeOptions}
            onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'type', value: e.target.value })}
            theme="metronic"
            style={{ width: '100%' }}
          />
        </div>
      </div>

      <TextInput
        label="Email Subject"
        value={template?.subject ?? ''}
        onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'subject', value: e.target.value })}
        theme="metronic"
        placeholder="Enter email subject"
        style={{ width: '100%', marginBottom: '10px' }}
        required={true}
      />
      <div
        style={{
          marginBottom: '10px',
          display: 'flex',
          flexDirection: 'column',
          flex: 1
        }}
      >
        {template?.type === 1 ? (
          <TextareaInput
            label="Email Body"
            required
            value={template?.content ?? ''}
            onChange={(e) => onTemplateUpdate({ guid: template?.id, name: 'content', value: e.target.value })}
            theme="metronic"
            height="300px"
            containerHeight="320px"
            style={{
              fontFamily: 'monospace',
              fontSize: '13px',
              resize: 'vertical'
            }}
          />
        ) : (
          <div style={{ flex: 1 }}>
            <Editor
              // value={jsonContent}
              name="content"
              onEditorReady={(editor) => setLexicalEditor(editor)}
              onChange={(name, currentContent, value) => handleLexicalChange(value)}
            />
          </div>
        )}
        <EmailVariablesSection emailVariables={emailVariables} />
      </div>
    </div>
  )
}

const EmailTemplatePreview = ({ content }) => {
  return (
    <div className={tStyle.previewBox}>
      <div className={tStyle.previewContent}>
        <div className="email-preview" dangerouslySetInnerHTML={{ __html: content ?? '' }}></div>
      </div>
    </div>
  )
}

const EmailVariablesSection = ({ emailVariables = [] }) => {
  if (emailVariables.length === 0) {
    return null
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        marginTop: '5px'
      }}
    >
      <Label label="Available Variables" theme="metronic" />
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '16px'
        }}
      >
        {emailVariables.map((group, groupIndex) => (
          <div key={groupIndex}>
            <div
              style={{
                fontSize: '12px',
                fontWeight: '600',
                color: '#495057',
                marginBottom: '6px'
              }}
            >
              {group.title}
            </div>
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '6px'
              }}
            >
              {group.values.map((variable, index) => (
                <span
                  key={index}
                  style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #dee2e6',
                    borderRadius: '3px',
                    padding: '3px 6px',
                    fontSize: '11px',
                    fontFamily: 'monospace',
                    color: '#495057',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => {
                    navigator.clipboard.writeText(`[${variable}]`)
                  }}
                  title={`Click to copy [${variable}] to clipboard`}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#e9ecef'
                    e.target.style.borderColor = '#adb5bd'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = '#f8f9fa'
                    e.target.style.borderColor = '#dee2e6'
                  }}
                >
                  [{variable}]
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
// Show default emails
// Create specific emails for approver and rejector
