/* TODO: Rename the rowOne, rowTwo, etc. to something more descriptive. */

.helpGuidesContainer {
  width: 100%;
  padding: 20px;
  border-radius: 10px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.backArrow {
  cursor: pointer;
  width: 45px;
  height: 45px;
  transition: opacity 0.2s ease;
}

.backArrow:hover {
  opacity: 0.7;
}

.breadCrumbContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 1rem;
}

.rowOne {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  color: white;
}

.rowOne h1 {
  margin-bottom: 0;
}

.rowOne p {
  margin-top: 0.5rem;
  width: 75%;
}

.submitCard {
  padding: 20px;
  width: 33%;
  color: white;
}

.submitCard h3 {
  margin: 0 0 10px;
}

.submitButton {
  background-color: #024F7C;
  color: white;
  border: none;
  padding: 10px 15px;
  margin-top: 10px;
  cursor: pointer;
  border-radius: 5px;
  font-size: 1rem;
}

.rowTwo {
  display: flex;
  gap: 1.5rem;
  align-items: flex-end;
  justify-content: space-around;
}

.searchBox {
  display: flex;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  height: 50px;
}

.searchBox input {
  flex: 1;
  padding: 10px;
  border: none;
  outline: none;
  font-size: 1rem;
}

.searchBox button {
  background-color: #024F7C;
  color: white;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.rowTwoLeft {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rowThree {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
}

.rowThree a {
  color: white;
  text-decoration: underline;
  font-size: 1rem;
}

.rowThree strong {
  color: white;
  font-size: 1rem;
}

/* Help Guide Cards Styles */
.helpCardsContainer {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.helpCard {
  background: white;
  border-radius: 12px;
  padding: 30px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.cardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
  width: 100%;
}

.cardIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cardIcon i {
  color: white;
  font-size: 24px;
}

.cardTitle {
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.cardContent {
  width: 100%;
  text-align: left;
}

.articleItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.2s ease;
}

.articleItem:hover {
  color: #024F7C;
}

.articleItem i {
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.articleItem span {
  flex: 1;
}

/* Article View Styles */
.articleContainer {
  background: white;
  border-radius: 8px;
  padding: 40px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.articleTitle {
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.articleContent {
  color: #555;
  line-height: 1.6;
  font-size: 0.95rem;
}

.articleContent h3 {
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 25px 0 15px 0;
}

.articleContent h4 {
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 20px 0 10px 0;
}

.articleContent p {
  margin-bottom: 15px;
}

.articleContent ul {
  margin: 10px 0 20px 20px;
  padding-left: 0;
}

.articleContent ol {
  margin: 10px 0 20px 20px;
  padding-left: 0;
}

.articleContent li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.articleFooter {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.helpfulSection {
  text-align: center;
}

.helpfulSection p {
  margin-bottom: 15px;
  color: #333;
  font-weight: 500;
}

.helpfulButtons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.thumbsButton {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbsButton:hover {
  background: #e9e9e9;
  border-color: #ccc;
}

.thumbsButton i {
  font-size: 16px;
  color: #666;
}