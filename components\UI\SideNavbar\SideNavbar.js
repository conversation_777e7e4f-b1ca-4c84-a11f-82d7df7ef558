import { MenuOption } from './MenuOption/MenuOption'
import { Menu } from './Menu/Menu'
import { SubMenu } from './SubMenu/SubMenu'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import React, { useEffect, useState } from 'react'
import { useGetTagNewWithFolderStructure } from '../../../api/ApiQueries'
import { StudentSideNavbar } from './StudentSideNavbar/StudentSideNavbar'
import { DmsSideNavbar } from './DmsSideNavbar/DmsSideNavbar'
import { ConditionalDisplay } from '../ConditionalDisplay/ConditionalDisplay'
import { useGetTeam } from '../../../hooks/useGetTeam'
import { useMsal } from '@azure/msal-react'
import Approvals from '../../../svg/Main Dashboard/Approvals.svg'
import RoutingApprovals from '../../../svg/Main Dashboard/Routing_Approval.svg'
import Myforms from '../../../svg/Main Dashboard/My_Forms.svg'
import Donorform from '../../../svg/Main Dashboard/Donor_form.svg'
import Mysubmission from '../../../svg/Main Dashboard/My_submissions.svg'
import Mymessages from '../../../svg/Main Dashboard/MyMessages_side menu.svg'
import report from '../../../svg/Main Dashboard/Reports.svg'
import Cogwheel from '../../../svg/Main Dashboard/Setting.svg'
import OrgStructure from '../../../svg/Main Dashboard/Org_Structure.svg'
import FolderStructure from '../../../svg/Main Dashboard/Folder_structure.svg'
import Student from '../../../svg/Main Dashboard/Student.svg'
import Staff from '../../../svg/Main Dashboard/Staff.svg'
import RolesManager from '../../../svg/Main Dashboard/Role_Manager.svg'
import DataManager from '../../../svg/Main Dashboard/Data_Manager.svg'
import PrivilegesManager from '../../../svg/Main Dashboard/Privileges_manager.svg'
import ControlsManager from '../../../svg/Main Dashboard/Controls_manager.svg'
import UserManager from '../../../svg/Main Dashboard/User_Manager.svg'
import Tenant from '../../../svg/Main Dashboard/Tenant_Role.svg'
import GroupManager from '../../../svg/Main Dashboard/Group_manager.svg'
import NotiManager from '../../../svg/Main Dashboard/Notification_Manager.svg'
import Inbox from '../../../svg/Main Dashboard/Inbox_Scanner.svg'
import DocFolder from '../../../svg/Main Dashboard/Doc_Repository.svg'
import MyPerformance from '../../../svg/My Performance.svg'
import OnboardingForms from '../../../images/OnboardingForms.png'
import MyTeams from '../../../svg/My Team.svg'
import MyAssets from '../../../images/MyAssets.png'
import ProjectForms from '../../../images/ProjectForms.png'
import writtenPaper from '../../../images/WrittenPaper.png'
import ProjectApproval from '../../../images/ProjectApproval.png'
import MyRequests from '../../../images/MyRequests.png'
import SoftwareRequests from '../../../images/SoftwareRequests.png'
import MyReviews from '../../../svg/My Reviews.svg'
import MyITRequests from '../../../images/MyITRequests.png'
import PerformanceForms from '../../../svg/Performance Forms.svg'
import PurchaseForms from '../../../svg/Main Dashboard/Purchase_Forms.svg'
import PurchaseApproval from '../../../svg/Main Dashboard/Purchase_Approvals.svg'
import Connectors from '../../../svg/Main Dashboard/Connectors.svg'
import Esign from '../../../svg/Main Dashboard/E-Sign.svg'
import MySignature from '../../../svg/Main Dashboard/My_Signature.svg'
import Request from '../../../svg/Main Dashboard/Requests.svg'
import Templates from '../../../svg/Main Dashboard/Templates.svg'
import Query from '../../../svg/Main Dashboard/Queries.svg'
import PurchaseRequisition from '../../../svg/Main Dashboard/Purchase_Requisition.svg'
import MyTickets from '../../../images/MyTickets.png'
import MyTicketReq from '../../../images/MyTicketReq.png'
import UserVerification from '../../../svg/Main Dashboard/Student_Verification.svg'
import Transcript from '../../../svg/Main Dashboard/Transcript.svg'
import Overview from '../../../public/svg/Common/Overview.svg'
import policiesProcedures from '../../../public/svg/Common/Policies & Procedures Library.svg'
import PerformanceConfig from '../../../svg/Performance Config.svg'
import ContractConfig from '../../../images/ContractConfig.png'
import NotificationsSetup from '../../../svg/Notification Email Setup.svg'
import HelpDeskConfig from '../../../images/HelpDeskConfig.png'
import AccessRequestConfig from '../../../images/AccessRequestConfig.png'
import Notify from '../../../svg/Main Dashboard/Notifi_Manager.svg'
import ReportBuilder from '../../../svg/Main Dashboard/Report_Builder.svg'
import vendorlist from '../../../svg/Main Dashboard/Vendor_List.svg'
import ItemList from '../../../svg/Main Dashboard/Item_List.svg'
import useActiveForm from '../../../hooks/useActiveForm'
import RoleBasedComponent from '../../../public/UserProfileContext/RoleBasedComponent'
import LeadSettings from '../../../svg/metronic/settings.svg'
import LeadSettingsActive from '../../../svg/metronic/settings_active.svg'
import LeadNoti from '../../../svg/metronic/notification.svg'
import LeadNotiActive from '../../../svg/metronic/notification_active.svg'
import List from '../../../svg/metronic/list.svg'
import ListActive from '../../../svg/metronic/list_active.svg'
import map from '../../../svg/metronic/mapping.svg'
import mapActive from '../../../svg/metronic/mapping_active.svg'
import LeadActive from '../../../svg/metronic/lead_active.svg'
import Lead from '../../../svg/metronic/lead.svg'
import ActiveVector from '../../../svg/metronic/Agent mapping filled.svg'
import Vector from '../../../svg/metronic/Vector.svg'
import Deal from '../../../svg/metronic/deal.svg'
import DealActive from '../../../svg/metronic/deal_active.svg'
import Campaign from '../../../svg/metronic/campaign.svg'
import CampaignActive from '../../../svg/metronic/campaign_active.svg'
import enrollment from '../../../svg/metronic/enrollment.svg'
import enrollmentActive from '../../../svg/metronic/enrollment_active.svg'
import LeadForms from '../../../svg/metronic/lead_form.svg'
import LeadFormsActive from '../../../svg/metronic/lead_form_active.svg'
import studentIcon from '../../../svg/metronic/student.svg'
import studentActive from '../../../svg/metronic/student_active.svg'
import ITSMMyTickets from '../../../images/itsm_my_tickets_icon.png'
import ITSMMyDevices from '../../../images/my_devices_icon.png'
import ITSMHelpGuides from '../../../images/help_guides_icon.png'
import ITSMSettings from '../../../images/itsm_settings_icon.png'

import styles from '../SideNavbar/SideNavbar.module.css'
import { useApiQuery } from '../../../api/apiRequest'
import { DmsEndpoints } from '../../../api/DMS/DmsEndpoints'

export default function SideNavbar({ solutionId = 1 }) {
  const router = useRouter()
  const currentPath = router.asPath
  const { activeForm } = useActiveForm(getDisplayMenu(currentPath) === 'Supplier' ? 4 : 6)

  const isOrgStructure = false

  const { data: folderStructure } = useApiQuery(
    ['DmsStructureFromSidenavbar', isOrgStructure],
    DmsEndpoints.GET_DMS_STRUCTURE,
    { isOrgStructure: isOrgStructure, requireGlobalTags: false },
    {
      onSuccess: (data) => console.log('Success fetching data:', data),
      onError: (error) => console.error('Error fetching data:', error)
    }
  )

  // const [nestedStudentData, setNestedStudentData] = useState([]);
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [ismobileorTab, setIsmobileorTab] = useState(window.innerWidth < 992)
  const [pin, setPin] = useState(false)

  const { accounts } = useMsal()
  const account = accounts[0]
  const { team } = useGetTeam(account)

  const [newNodes, setNewNodes] = useState([
    {
      key: process.env.NEXT_PUBLIC_DMS_ORGNAME,
      label: process.env.NEXT_PUBLIC_DMS_ORGNAME,
      icon: 'pi pi-fw pi-inbox',
      data: 'Resume Document',
      command: () => {
        router.push({
          pathname: '/document/search',
          query: { data: process.env.NEXT_PUBLIC_DMS_ORGNAME }
        })
      }
    }
  ])

  const [isMenuVisible, setIsMenuVisible] = useState(true)
  const handleMenuOptionClick = (path) => {
    console.log('path', router.query, router.asPath, router, path)
    setPin(path !== '/document/RoutingApproval' && path?.includes('document'))
    if (ismobileorTab) {
      setIsCollapsed(true)
    }
  }

  // const { activeForm } = useActiveForm();

  useEffect(() => {
    console.log('router', router)
    const pathDms = router?.pathname !== '/document/RoutingApproval' && router?.pathname?.includes('document')
    setPin(pathDms)
    setTimeout(() => {
      toggleCollapse(!pathDms)
    }, 4000)
  }, [router])

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 992) {
        setIsmobileorTab(true)
        console.log('true')
      } else {
        setIsmobileorTab(false)
        console.log('false')
      }
    }
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  let selectedKey = {}
  let setSelectedKey
  ;[selectedKey, setSelectedKey] = useState({})

  function toggleCollapse(value) {
    setIsCollapsed((prev) => value ?? !prev)
  }

  function getDisplayMenu(path) {
    if (path.startsWith('/Poilcy-and-procedures') || solutionId === 9) {
      return 'PoilcyAndProcedures'
    }
    if (path.startsWith('/PerformanceReview') || solutionId === 2) {
      return 'PerformanceReview'
    }
    if (path.startsWith('/ProjectRequisition') || solutionId === 5) {
      return 'ProjectRequisition'
    }
    if (path.startsWith('/PurchaseOrder') || solutionId === 6) {
      return 'PurchaseOrder'
    }
    if (path.startsWith('/MyTickets')) {
      return 'MyTickets'
    }
    if (path.startsWith('/OnBoarding') || solutionId === 3) {
      return 'OnBoarding'
    }
    if (path.startsWith('/AccessRequest') || solutionId === 7) {
      return 'AccessRequest'
    }
    if (path.startsWith('/AssetTracker') || solutionId === 10) {
      return 'AssetTracker'
    }
    if (path.startsWith('/JobRequestion')) {
      return 'JobRequestion'
    }
    if (path.startsWith('/Supplier') || solutionId === 4) {
      return 'Supplier'
    }
    if (path.startsWith('/LeadGeneration') || solutionId === 11) {
      return 'LeadGeneration'
    }
    if (path.startsWith('/LMS') || solutionId === 12) {
      return 'LMS'
    }
    if (path.startsWith('/InstructorLMS') || solutionId === 13) {
      return 'InstructorLMS'
    }
    if (path.startsWith('/Contracts')) {
      return 'Contracts'
    }
    if (path.startsWith('/ITSM')) {
      return 'ITSM'
    }
    return 'main'
  }

  const activeSection = getDisplayMenu(currentPath)

  const alphabets = Array.from({ length: 26 }, (_, i) => ({
    id: (i + 1).toString(),
    name: String.fromCharCode(65 + i)
  }))

  function capitalize(string) {
    return string.charAt(0).toUpperCase() + string.slice(1)
  }

  // Ahmet: Update the below html with <ConditionalDisplay> component instead of using like this { condition && <Component /> }
  // Also Create a component in this file for each active section
  return (
    <ConditionalDisplay condition={isMenuVisible}>
      <>
        <Menu
          activeSection={activeSection}
          isCollapsed={ismobileorTab ? false : isCollapsed}
          toggleCollapse={toggleCollapse}
          pin={pin}
          setPin={setPin}
          metronic={
            activeSection === 'LeadGeneration' ||
            activeSection === 'LMS' ||
            activeSection === 'InstructorLMS' ||
            activeSection === 'ITSM'
          }
        >
          <ConditionalDisplay condition={activeSection === 'main'}>
            <MenuOption
              label="Approvals"
              roles={['Admin', 'Contributor', 'Viewer']}
              iconPath={Approvals}
              path={'/ApprovalPage'}
              isVisibleToManager={true}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Routing Approvals"
              roles={['Admin', 'Contributor', 'Viewer']}
              iconPath={RoutingApprovals}
              path={'/dms/Routing/RoutingApproval'}
              isVisibleToManager={true}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Forms"
              roles={['Admin', 'Designer']}
              iconPath={Myforms}
              path={'/'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Submissions"
              roles={['Admin', 'Creator', 'Viewer', 'Contributor', 'Designer']}
              iconPath={Mysubmission}
              path={'/MySubmissions'}
              isVisibleToManager={true}
              isVisibleToEndUser={true}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />

            <SubMenu
              isCollapsed={isCollapsed}
              label="eSign"
              defaultOpen={false}
              roles={[]}
              isVisibleToEndUser={true}
              isVisibleToManager={true}
              iconPath={Esign}
              // basePath={"/esignature"}
              // iconPath={Folder}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label="My Signature"
                roles={[]}
                iconPath={MySignature}
                // iconPath={Folder}
                isVisibleToEndUser={true}
                path={'/esignature/my-signature'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />

              <MenuOption
                label="Requests"
                roles={['Admin', 'Designer']}
                iconPath={Request}
                // iconPath={Folder}
                path={'/esignature/request'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Templates"
                roles={['Admin', 'Designer']}
                iconPath={Templates}
                // iconPath={Folder}
                path={'/esignature/templates'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
            <MenuOption
              label="Reports"
              roles={['Admin']}
              iconPath={report}
              // iconPath={Folder}
              path={''}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Queries"
              roles={['Admin']}
              iconPath={Query}
              path={'/Queries'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="File Routing"
              roles={['Admin', 'Designer']}
              iconPath={report}
              path={'/dms/Routing/FileRouting'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <RoleBasedComponent accelatorId={1} moduleId={3} responsibilityId={2} type={4}>
                <MenuOption
                  label="Org Structure"
                  roles={['Admin', 'Contributor', 'Creator', 'Designer', 'Viewer']}
                  // isVisibleToManager={true}
                  iconPath={OrgStructure}
                  path={'/dms/OrgStructure'}
                  toggleMenu={handleMenuOptionClick}
                  accelerator={1}
                  module={3}
                  onlyForAdmin={true}
                  isCollapsed={isCollapsed}
                />

                <MenuOption
                  label="Folder Structure"
                  roles={['Admin', 'Contributor', 'Creator', 'Designer', 'Viewer']}
                  // isVisibleToManager={true}
                  iconPath={FolderStructure}
                  path={'/dms/FolderStructure'}
                  toggleMenu={handleMenuOptionClick}
                  accelerator={1}
                  module={3}
                  onlyForAdmin={true}
                  isCollapsed={isCollapsed}
                />
              </RoleBasedComponent>
              {/* <MenuOption
                label="Roles Manager"
                roles={["Admin"]}
                iconPath={RolesManager}
                path={"/RolesManager"}
                toggleMenu={handleMenuOptionClick}
              /> */}

              <MenuOption
                label="Roles Manager"
                roles={['Admin']}
                iconPath={RolesManager}
                path={'/RoleManager'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                accelerator={1}
                module={3}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Data Access Control Manager"
                roles={['Admin']}
                iconPath={DataManager}
                path={'/DataAccessControl'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />

              <MenuOption
                label="Privileges Manager"
                roles={['Admin']}
                iconPath={PrivilegesManager}
                path={'/PrivilegesManager'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="User Manager"
                roles={['Admin']}
                iconPath={UserManager}
                path={'/UserManagement'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Tenant Role"
                roles={['Admin']}
                iconPath={Tenant}
                path={'/TenantRole'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Connectors"
                roles={['Admin']}
                iconPath={Connectors}
                path={'/Connectors'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Group Manager"
                roles={['Admin']}
                iconPath={GroupManager}
                path={'/GroupManager'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Notification Manager"
                roles={['Admin']}
                iconPath={NotiManager}
                path={'/template/formEmailSettings'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Inbox Scanner"
                roles={['Admin']}
                iconPath={Inbox}
                //path={"/InboxScanner"}
                path={'/dms/InboxMonitoring'}
                toggleMenu={handleMenuOptionClick}
                accelerator={0}
                module={0}
                isCollapsed={isCollapsed}
              />
              <SubMenu
                label="Miscellaneous"
                roles={['Admin', 'Actor', 'Approver', 'Designer']}
                iconPath={Cogwheel}
                basePath={'settings'}
                toggleMenu={handleMenuOptionClick}
              >
                <MenuOption
                  label="Transcript"
                  roles={['Admin']}
                  iconPath={Transcript}
                  path={'/Transcript'}
                  toggleMenu={handleMenuOptionClick}
                  isCollapsed={isCollapsed}
                />
                <MenuOption
                  label="Donor Form"
                  roles={['Admin', 'Designer']}
                  iconPath={Donorform}
                  path={'/document/donar'}
                  toggleMenu={handleMenuOptionClick}
                  isCollapsed={isCollapsed}
                />
                <MenuOption
                  label="Student Verification"
                  roles={['Admin']}
                  iconPath={UserVerification}
                  path={'/Jumio-Identity-Check'}
                  toggleMenu={handleMenuOptionClick}
                  isCollapsed={isCollapsed}
                />
              </SubMenu>
              <MenuOption
                label="Reason Code"
                roles={['Admin']}
                iconPath={Inbox}
                path={'/ReasonCode'}
                toggleMenu={handleMenuOptionClick}
                onlyForAdmin={true}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
            <RoleBasedComponent accelatorId={1} moduleId={3} responsibilityId={1} type={4}>
              <SubMenu
                isCollapsed={isCollapsed}
                label={'InnovDocs'}
                isLowerCase={true}
                roles={['Admin', 'Contributor', 'Creator', 'Designer', 'Viewer']}
                iconPath={DocFolder}
                initiallyOpen={true}
                path={`/dms/InnovDocs/${folderStructure?.dmsStructure?.[0]?.id}/${folderStructure?.dmsStructure?.[0]?.level}`}
                toggleMenu={handleMenuOptionClick}
              >
                <ConditionalDisplay condition={folderStructure?.dmsStructure}>
                  {folderStructure?.dmsStructure?.[0]?.childList
                    .filter((item) => item.isGlobalTag === true)
                    .map((data, index) => {
                      return (
                        <SubMenu
                          key={index}
                          label={capitalize(data.name)}
                          isCollapsed={isCollapsed}
                          roles={['Admin', 'Contributor', 'Creator', 'Designer', 'Viewer']}
                          isVisibleToManager={true}
                          iconPath={data.name === 'staff' ? Staff : Student}
                          path={`/dms/InnovDocs/MandatoryTags/${data?.type}/0`}
                          toggleMenu={handleMenuOptionClick}
                        >
                          {alphabets.map((item, index) => (
                            <StudentSideNavbar
                              key={index}
                              data={item}
                              toggleMenu={handleMenuOptionClick}
                              type={data?.type}
                              isCollapsed={isCollapsed}
                            />
                          ))}
                        </SubMenu>
                      )
                    })}
                  <DmsSideNavbar data={folderStructure?.dmsStructure?.[0]} toggleMenu={handleMenuOptionClick} />
                </ConditionalDisplay>
              </SubMenu>
            </RoleBasedComponent>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'PerformanceReview'}>
            <MenuOption
              label="My Performance"
              roles={['Admin', 'Contributor', 'Creator', 'Designer', 'Viewer']}
              iconPath={MyPerformance}
              path={'/PerformanceReview'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Reviews"
              roles={['Admin']}
              isVisibleToManager={true}
              iconPath={MyReviews}
              path={'/PerformanceReview/MyReviews'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <ConditionalDisplay condition={team && team?.count > 0}>
              <MenuOption
                label="My Team"
                roles={['Admin', 'Contributor', 'Creator', 'Designer', 'Viewer']}
                iconPath={MyTeams}
                path={'/PerformanceReview/MyTeam'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </ConditionalDisplay>
            <MenuOption
              label="Performance Forms"
              roles={['Admin', 'Designer']}
              iconPath={PerformanceForms}
              path={'/PerformanceReview/MyPerformanceForms'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin', 'Actor', 'Approver', 'Designer']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label="Performance Config"
                roles={['Admin', 'Definer', 'Designer']}
                iconPath={PerformanceConfig}
                path={'/PerformanceReview/Config'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Notification Email Setup"
                roles={['Admin']}
                iconPath={NotificationsSetup}
                path={'/PerformanceReview/Notifications'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="User Manager"
                roles={['Admin']}
                iconPath={UserManager}
                path={'/PerformanceReview/UserManager'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Queries"
                roles={['Admin']}
                iconPath={Query}
                path={'/PerformanceReview/Queries'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'ProjectRequisition'}>
            <MenuOption
              label="Approvals"
              roles={['Admin', 'Contributor']}
              iconPath={ProjectApproval}
              path={'/ProjectRequisition/MyApprovalRequest'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Requests"
              roles={['Admin', 'Contributor', 'Creator', 'Designer']}
              iconPath={MyRequests}
              path={'/ProjectRequisition'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Forms"
              roles={['Admin', 'Designer']}
              iconPath={ProjectForms}
              path={'/ProjectRequisition/MyFormsRequisition'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'PoilcyAndProcedures'}>
            <MenuOption
              label={'Overview'}
              roles={['Admin', 'Creator', 'Contributor', 'Designer']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={Overview}
              iconStyle={{ width: '1.75rem', height: '1.75rem' }}
              path={'/Poilcy-and-procedures'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Policies & Procedures Library'}
              roles={['Admin', 'Creator', 'Contributor', 'Designer']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={policiesProcedures}
              path={'/Poilcy-and-procedures/MySubmissions'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Approvals'}
              roles={['Admin', 'Contributor', 'Designer']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={Approvals}
              path={'/Poilcy-and-procedures/Approvals'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Master form'}
              roles={['Admin', 'Designer']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={Myforms}
              path={'/Poilcy-and-procedures/Master'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Queries'}
              roles={['Admin']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={Query}
              path={'/Poilcy-and-procedures/Queries'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Notification manager'}
              roles={['Admin', 'Designer']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={Notify}
              path={'/Poilcy-and-procedures/NotificationManager'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'PurchaseOrder'}>
            <MenuOption
              label={'Purchase Requisition'}
              roles={['Admin', 'Contributor']}
              isVisibleToManager={false}
              isVisibleToEndUser={false}
              iconPath={PurchaseRequisition}
              path={'/PurchaseOrder'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Purchase Approvals'}
              roles={['Admin', 'Contributor', 'Designer']}
              isVisibleToManager={true}
              isVisibleToEndUser={false}
              iconPath={PurchaseApproval}
              path={'/PurchaseOrder/Approval'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Requests"
              roles={['Admin', 'Designer', 'Contributor', 'Creator']}
              iconPath={Mysubmission}
              path={'/PurchaseOrder/MyRequest'}
              isVisibleToManager={true}
              isVisibleToEndUser={false}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Requisition Master'}
              roles={['Admin', 'Designer']}
              iconPath={PurchaseForms}
              isVisibleToEndUser={false}
              isVisibleToManager={false}
              path={`/PurchaseOrder/${activeForm}`}
              toggleMenu={() => handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin', 'Designer']}
              isVisibleToEndUser={false}
              isVisibleToManager={false}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Notification Manager'}
                roles={['Admin', 'Designer']}
                iconPath={Notify}
                path={'/PurchaseOrder/Setting/NotificationManager'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              {/* <MenuOption
                label={"Report Bulider"}
                roles={["Admin"]}
                iconPath={ReportBuilder}
                path={"/"}
                toggleMenu={handleMenuOptionClick}
              />
              <MenuOption
                label={"Role Permission"}
                roles={["Admin"]}
                iconPath={RolePermission}
                path={"/"}
                toggleMenu={handleMenuOptionClick}
              /> */}
              <MenuOption
                label={'Supplier List'}
                roles={['Admin', 'Designer']}
                iconPath={vendorlist}
                path={'/PurchaseOrder/Setting/Vendor'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Item List'}
                roles={['Admin', 'Designer']}
                iconPath={ItemList}
                path={'/PurchaseOrder/Setting/ItemsList'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'MyTickets'}>
            <MenuOption
              label={'My Tickets'}
              roles={[]}
              iconPath={MyTickets}
              path={'/MyTickets'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'My Ticket Request'}
              roles={[]}
              iconPath={MyTicketReq}
              path={'/MyTickets/MyTicketRequest'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label={'Settings'}
              defaultOpen={false}
              roles={['Admin', 'Designer']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Help Desk Config'}
                roles={['Admin', 'Designer']}
                iconPath={HelpDeskConfig}
                path={'/MyTickets/Config'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Notification Email Setup'}
                roles={['Admin', 'Designer']}
                iconPath={NotificationsSetup}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'User Manager'}
                roles={['Admin', 'Designer']}
                iconPath={UserManager}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Queries'}
                roles={['Admin', 'Designer']}
                iconPath={Query}
                path={'/MyTickets/Queries'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'AccessRequest'}>
            <MenuOption
              label={'Software Request'}
              roles={['Admin', 'Contributor', 'Creator']}
              iconPath={SoftwareRequests}
              isVisibleToEndUser={true}
              path={'/AccessRequest'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'My Requests'}
              roles={['Admin', 'Contributor', 'Designer']}
              iconPath={MyITRequests}
              path={'/AccessRequest/MyRequests'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'My Forms'}
              roles={['Admin', 'Designer']}
              iconPath={writtenPaper}
              path={'/AccessRequest/AccessForms'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label={'Settings'}
              defaultOpen={false}
              roles={['Admin', 'Designer']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Access Config'}
                roles={['Admin', 'Designer']}
                iconPath={AccessRequestConfig}
                path={'/AccessRequest/Config'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Notification Email Setup'}
                roles={['Admin', 'Designer']}
                iconPath={NotificationsSetup}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'User Manager'}
                roles={['Admin', 'Designer']}
                iconPath={UserManager}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Queries'}
                roles={['Admin', 'Designer']}
                iconPath={Query}
                path={'/AccessRequest/Queries'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'OnBoarding'}>
            <MenuOption
              label={'OnBoarding Forms'}
              roles={['Admin', 'Designer']}
              iconPath={OnboardingForms}
              path={'/OnBoarding/OnBoardingForms'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'My Requests'}
              roles={['Admin']}
              isVisibleToManager={true}
              iconPath={Mysubmission}
              path={'/OnBoarding/MyRequests'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <ConditionalDisplay condition={team && team?.count > 0}>
              <MenuOption
                label={'My Team'}
                roles={['Admin', 'Contributor', 'Designer', 'Creator', 'Viewer']}
                iconPath={MyTeams}
                path={'/OnBoarding/MyTeam'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </ConditionalDisplay>
            <MenuOption
              label={'My Assets'}
              roles={['Admin']}
              isVisibleToEndUser={true}
              iconPath={MyAssets}
              path={'/OnBoarding'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Approvals'}
              roles={['Admin', 'Contributor']}
              iconPath={Approvals}
              path={'/OnBoarding/Approvals'}
              toggleMenu={handleMenuOptionClick}
              isVisibleToManager={true}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Notification Manager'}
              roles={['Admin']}
              iconPath={NotiManager}
              path={'/OnBoarding/Notifications'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Queries'}
              roles={['Admin']}
              iconPath={Query}
              path={'/OnBoarding/Queries'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label={'Settings'}
              defaultOpen={false}
              roles={['Admin']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Onboarding Configuration'}
                roles={['Admin']}
                iconPath={Cogwheel}
                path={'/OnBoarding/Settings/LineItemConfiguration'}
                toggleMenu={handleMenuOptionClick}
              />
              <MenuOption
                label={'Line Item Configuration'}
                roles={['Admin']}
                iconPath={Cogwheel}
                path={'/OnBoarding/Settings/StatusConfiguration'}
                toggleMenu={handleMenuOptionClick}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'JobRequestion'}>
            <MenuOption
              label="Dashboard"
              roles={['Admin', 'Contributor', 'Creator']}
              iconPath={report}
              path={'/JobRequestion'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Forms"
              roles={['Admin']}
              iconPath={Myforms}
              path={'/JobRequestion/Forms'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Approvals"
              roles={['Admin', 'Contributor']}
              iconPath={Approvals}
              path={'/JobRequestion/Approvals'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Submissions"
              roles={['Admin', 'Creator', 'Contributor']}
              iconPath={Mysubmission}
              path={'/JobRequestion/MySubmissions'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label="User Manager"
                roles={['Admin']}
                iconPath={UserManager}
                path={'/UserManagement'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Notification Manager"
                roles={['Admin']}
                iconPath={NotiManager}
                path={'/template'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'Supplier'}>
            <MenuOption
              label="Supplier Qualification"
              roles={['Admin', 'Contributor', 'Creator']}
              iconPath={PurchaseRequisition}
              // isVisibleToEndUser={true}
              path={'/Supplier'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Supplier Approvals"
              roles={['Admin', 'Contributor']}
              iconPath={PurchaseApproval}
              isVisibleToManager={true}
              // isVisibleToEndUser={true}
              path={'/Supplier/Approval'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Request"
              roles={['Admin', 'Viewer', 'Contributor', 'Designer', 'End User']}
              iconPath={Mysubmission}
              path={'/Supplier/MyRequest'}
              isVisibleToEndUser={true}
              isVisibleToManager={true}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Qualifaction Master"
              roles={['Admin', 'Designer']}
              iconPath={PurchaseForms}
              path={`/Supplier/${activeForm}`}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin']}
              iconPath={Cogwheel}
              basePath={'settings'}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Notification Manager'}
                roles={['Admin']}
                iconPath={Notify}
                path={'#'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Report Bulider'}
                roles={['Admin']}
                iconPath={ReportBuilder}
                path={'#'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'LeadGeneration'}>
            <MenuOption
              label="Prospects"
              roles={['Viewer']}
              iconPath={currentPath === '/LeadGeneration' ? LeadActive : Lead}
              path={'/LeadGeneration'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Students"
              roles={['Viewer']}
              iconPath={currentPath === '/LeadGeneration/Contacts' ? studentActive : studentIcon}
              path={'/LeadGeneration/Contacts'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            {/* <MenuOption
              label="Accounts"
              roles={["Viewer"]}
              iconPath={LeadSettings}
              path={"/Contracts"}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
            /> */}
            <MenuOption
              label="Deals"
              roles={['Viewer']}
              iconPath={currentPath === '/Contracts' ? DealActive : Deal}
              path=""
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Forms"
              roles={['Viewer']}
              iconPath={currentPath === '/Contracts' ? LeadFormsActive : LeadForms}
              path=""
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Contact Lists"
              roles={['Viewer']}
              iconPath={currentPath === '/LeadGeneration/Lists' ? ListActive : List}
              path={'/LeadGeneration/Lists'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Campaigns"
              roles={['Viewer']}
              iconPath={currentPath === '/LeadGeneration/Campaigns' ? CampaignActive : Campaign}
              path={'/LeadGeneration/Campaigns'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Enrollment Applications"
              roles={['Viewer']}
              iconPath={currentPath === '/LeadGeneration/EnrollmentApplications' ? enrollmentActive : enrollment} // TODO: Add icon
              path={'/LeadGeneration/EnrollmentApplications'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Settings"
              roles={['Viewer']}
              iconPath={currentPath === '/Contracts' ? LeadSettingsActive : LeadSettings}
              path=""
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              onlyForAdmin={true}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Program Mapping'}
              roles={['Viewer']}
              iconPath={currentPath === '/LeadGeneration/ProgramMapping' ? mapActive : map}
              path={'/LeadGeneration/ProgramMapping'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              onlyForAdmin={true}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Notifications Manager"
              roles={['Admin']}
              iconPath={currentPath === '/template' ? LeadNotiActive : LeadNoti}
              path={''}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'LMS'}>
            <MenuOption
              label="Home"
              roles={['Viewer']}
              iconPath={currentPath === '/LMS' ? LeadActive : Lead}
              path={'/LMS'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Available Courses"
              roles={['Viewer']}
              iconPath={currentPath === '/LMS/AvailableCourses' ? studentActive : studentIcon}
              path={'/LMS/AvailableCourses'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Courses"
              roles={['Viewer']}
              iconPath={currentPath === '/LMS/MyCourses' ? studentActive : studentIcon}
              path={'/LMS/MyCourses'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Certificates"
              roles={['Viewer']}
              iconPath={currentPath === '/LMS/MyCertificates' ? DealActive : Deal}
              path="/LMS/MyCertificates"
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Profile"
              roles={['Viewer']}
              iconPath={currentPath === '/LMS/MyProfile' ? LeadFormsActive : LeadForms}
              path="/LMS/MyProfile"
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'InstructorLMS'}>
            <MenuOption
              label="Home"
              roles={['Viewer']}
              iconPath={currentPath === '/InstructorLMS' ? LeadActive : Lead}
              path={'/InstructorLMS'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Courses"
              roles={['Viewer']}
              iconPath={currentPath === '/InstructorLMS/Courses' ? studentActive : studentIcon}
              path={'/InstructorLMS/Courses'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Profile"
              roles={['Viewer']}
              iconPath={currentPath === '/InstructorLMS/Profile' ? LeadFormsActive : LeadForms}
              path={'/InstructorLMS/Profile'}
              toggleMenu={handleMenuOptionClick}
              theme="metronic"
              isCollapsed={isCollapsed}
            />
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'Contracts'}>
            <MenuOption
              label="My Submissions"
              roles={['Viewer']}
              iconPath={Mysubmission}
              path={'/Contracts'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Supplier Approvals"
              roles={['Admin']}
              iconPath={Approvals}
              path={'/Contracts/SupplyApprovals'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Contract Approvals"
              roles={['Admin']}
              iconPath={Approvals}
              path={'/Contracts/ContractApprovals'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Contracts"
              roles={['Admin']}
              iconPath={Myforms}
              path={'/Contracts/MyContracts'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="eSign"
              defaultOpen={false}
              roles={[]}
              isVisibleToEndUser={true}
              isVisibleToManager={true}
              iconPath={Esign}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label="My Signatures"
                roles={['Viewer']}
                iconPath={MySignature}
                // iconPath={Folder}
                isVisibleToEndUser={true}
                path={'/Contracts/MySignatures'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Requests"
                roles={['Admin']}
                iconPath={Request}
                path={'/Contracts/Requests'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Templates"
                roles={['Admin']}
                iconPath={Templates}
                path={'/Contracts/Templates'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin']}
              isVisibleToEndUser={true}
              isVisibleToManager={true}
              iconPath={Cogwheel}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Contract Config'}
                roles={['Admin', 'Designer']}
                iconPath={ContractConfig}
                path={'/Contracts/Config'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Notification Email Setup'}
                roles={['Admin', 'Designer']}
                iconPath={NotificationsSetup}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'User Manager'}
                roles={['Admin', 'Designer']}
                iconPath={UserManager}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Queries'}
                roles={['Admin', 'Designer']}
                iconPath={Query}
                path={'/Contracts/Queries'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'AssetTracker'}>
            {/* TODO: Update menu options after implementing other screens. */}
            <MenuOption
              label={'Asset Master'}
              roles={['Admin']}
              iconPath={Myforms}
              path={'/AssetTracker'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label={'Asset Transactions'}
              roles={['Admin', 'Creator']}
              iconPath={Approvals}
              path={'/'}
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              isCollapsed={isCollapsed}
              label="Settings"
              defaultOpen={false}
              roles={['Admin']}
              isVisibleToManager={true}
              iconPath={Cogwheel}
              toggleMenu={handleMenuOptionClick}
            >
              <MenuOption
                label={'Asset Config'}
                roles={['Admin']}
                iconPath={PerformanceConfig}
                path={'/'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Notification Manager'}
                roles={['Admin']}
                iconPath={NotiManager}
                path={'/template'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'User Manager'}
                roles={['Admin']}
                iconPath={UserManager}
                path={'/UserManagement'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label={'Queries'}
                roles={['Admin']}
                iconPath={Query}
                path={'/Queries'}
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
          <ConditionalDisplay condition={activeSection === 'ITSM'}>
            <MenuOption
              label="My Tickets"
              roles={['Admin', 'Creator', 'Designer', 'Contributor', 'Viewer']}
              iconPath={ITSMMyTickets}
              path={'/ITSM'}
              theme="metronic"
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Queue Dashboard"
              roles={['Admin', 'Creator', 'Designer', 'Contributor', 'Viewer']}
              iconPath={ITSMMyTickets} // TODO: Need a new icon for this.
              path={'/ITSM/queueDashboard'}
              theme="metronic"
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="My Devices"
              roles={['Admin', 'Creator', 'Designer', 'Contributor', 'Viewer']}
              iconPath={ITSMMyDevices}
              path={'/ITSM/myDevices'}
              theme="metronic"
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <MenuOption
              label="Help Guides"
              roles={['Admin', 'Creator', 'Designer', 'Contributor', 'Viewer']}
              iconPath={ITSMHelpGuides}
              path={'/ITSM/helpGuides'}
              theme="metronic"
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            />
            <SubMenu
              label="" // TODO: Add Settings after updating Submenu to metronic theme.
              roles={['Admin']}
              iconPath={ITSMSettings}
              theme="metronic"
              toggleMenu={handleMenuOptionClick}
              isCollapsed={isCollapsed}
            >
              <MenuOption 
                label="Category Settings"
                roles={['Admin']}
                iconPath={ITSMSettings}
                path={'/ITSM/settings/categorySettings'}
                theme="metronic"
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
              <MenuOption
                label="Queue Management"
                roles={['Admin']}
                iconPath={ITSMSettings}
                path={'/ITSM/settings/queueManagement'}
                theme="metronic"
                toggleMenu={handleMenuOptionClick}
                isCollapsed={isCollapsed}
              />
            </SubMenu>
          </ConditionalDisplay>
        </Menu>
      </>
    </ConditionalDisplay>
  )
}
