import clsx from "clsx"
import useUtilityFunctions from "../../../../hooks/useUtilityFunctions" 
import { SettingsLabel } from "../../Settings/UI/SettingsLabel/SettingsLabel"
import { MultiSelect } from "primereact/multiselect"
import { useFetchSourceForm } from "../../../../hooks/useFetchSourceForm"

import styles from "../../../../styles/BuilderSwimLaneStyles/SettingsMenu.module.css"

export const ResultFields = ({ inputs, handleInputChange, isSpreadsheet = false }) => {
  const { filterObjectsByGuid, transformResultFields } = useFetchSourceForm();
  const { createTargetObject } = useUtilityFunctions();
  const isDisabled = !inputs?.primarySearchField;
  
  const itemTemplate = (option) => {
    const handleColumnChange = (e) => {
      // Create a deep copy of current resultFields
      const copy = JSON.parse(JSON.stringify(inputs.resultFields));
      
      // Find the matching element in the copy
      const elementToUpdate = copy.find(item => item.columnValue === option.columnValue);
      
      // Update the columnValue of the found element
      if (elementToUpdate) {
        elementToUpdate.columnValue = e.target.value;
      }

      // Update the state through handleInputChange
      handleInputChange(createTargetObject(`${inputs?.guid}.resultFields`, copy));
    };
  
    return (
      <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", gap: "6rem" }}>
        <div style={{ width: "150px" }}>{option.label}</div>
        <select 
          style={{ width: "100px" }}
          value={option.columnValue} 
          onChange={handleColumnChange}
        >
          {option.columnList.map((letter) => (
            <option key={letter} value={letter}>
              {letter}
            </option>
          ))}
        </select>
      </div>
    )
  }

    return (
      <>
        <SettingsLabel label="Result Fields" isRequired={true} />
        <div style={{ position: 'relative' }}>
          <MultiSelect 
            className={clsx(styles.fullWidth, "p-tempStyle", isDisabled && styles.disabledField)} 
            name={`${inputs?.guid}.resultFields`} 
            value={inputs?.resultFields ?? false} 
            onChange={handleInputChange} 
            options={isSpreadsheet ?
              transformResultFields(filterObjectsByGuid(inputs?.primarySearchField?.guid, inputs?.sourceFormData?.data)) :
              filterObjectsByGuid(inputs?.primarySearchField?.guid, inputs?.sourceFormData?.data)
            } 
            optionLabel="label"
            disabled={isDisabled}
            itemTemplate={isSpreadsheet ? itemTemplate : undefined}
          />
        </div>
      </>
    )
}
