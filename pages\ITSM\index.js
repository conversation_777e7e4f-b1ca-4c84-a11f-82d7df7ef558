import { PageContainer } from '../../components/UI/Page/PageContainer/PageContainer'
import { useRouter } from 'next/router'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { ConditionalDisplay } from '../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { useState, useContext, useEffect } from 'react'
import { AdvancedFileUpload } from '../../components/FormBuilder/SharedComponents/File/AdvancedFileUpload/AdvancedFileUpload'
import { InputText } from 'primereact/inputtext'
import { useInputs } from '../../hooks/useInput'
import { useSearchParams } from 'next/navigation'
import UserProfileContext from '../../public/UserProfileContext/UserProfileContext'
import TextInput from '../../components/UI/Input/TextInput/TextInput'
import TextareaInput from '../../components/UI/Input/TextareaInput/TextareaInput'
import Image from 'next/image'
import BreadCrumbs from '../../components/UI/BreadCrumbs/BreadCrumbs'
import Button from '../../components/UI/Button/Button'
import clsx from 'clsx'

import Backarrow from '../../svg/metronic/back_metronic.svg'
import ITSMTicket from '../../images/submit_new_ticket_icon.png'
import ITSMTicketDrafts from '../../images/itsm_ticket_drafts_icon.png'
import ITSMTicketInProgress from '../../images/itsm_ticket_in_progress_icon.png'
import ITSMTicketCompleted from '../../images/itsm_ticket_completed_icon.png'
import ITSMOracleIcon from '../../images/itsm_oracle_icon.png'
import ITSMWorkdayIcon from '../../images/itsm_workday_icon.png'
import ITSMSalesforceIcon from '../../images/itsm_salesforce_icon.png'
import ITSMCanvasIcon from '../../images/itsm_canvas_icon.png'
import ITSMWarningIcon from '../../images/itsm_warning_icon.png'
import ITSMSupportTicket from '../../images/itsm_support_ticket_request.png'
import PersonIcon from '../../svg/metronic/blue_profile.svg'
import PhoneIcon from '../../svg/metronic/phone.svg'
import MailIcon from '../../svg/metronic/mail.svg'
import CalendarIcon from '../../images/itsm_date_icon.png'

import styles from './index.module.css'
import SelectInput from '../../components/UI/Input/SelectInput/SelectInput'

export default function ITSM() {
  const router = useRouter()
  const searchParams = useSearchParams()
  console.log('searchParams', searchParams.get('tab'))
  const [isMyTicketDashboard, setIsMyTicketDashboard] = useState(searchParams.get('tab') !== 'submitNewTicket')
  const userProfile = useContext(UserProfileContext)
  // TODO: Refactor the state management once the backend is ready.
  const [firstName, setFirstName] = useState('John') // userProfile.firstName
  const [lastName, setLastName] = useState('Smith') // userProfile.lastName
  const [email, setEmail] = useState('@siu.edu') // userProfile.email
  const [phone, setPhone] = useState('************') // userProfile.mobileNumber
  const { inputs, files, handleInputChange } = useInputs({ initialValues: { fileValues: [] } })

  // useEffect(() => {
  //   setFirstName(userProfile.firstName)
  //   setLastName(userProfile.lastName)
  //   setEmail(userProfile.email)
  //   setPhone(userProfile.mobileNumber)
  // }, [userProfile])

  return (
    <PageContainer theme="metronic">
      <div className={styles.myTicketsTopContainer}>
        <Image src={Backarrow} alt="Back" onClick={() => router.back()} />
        <BreadCrumbs title="My Tickets" breadcrumbItems={[{ label: 'Dashboard' }]} theme="metronic" />
        <div className={styles.newTicketContainer}>
          <Button
            label={isMyTicketDashboard ? 'Submit New Ticket' : 'Submit'}
            theme="metronic"
            width={isMyTicketDashboard ? '235px' : '180px'}
            icon={isMyTicketDashboard ? <Image src={ITSMTicket} alt="ITSM Ticket" /> : <i className="pi pi-check" />}
            onClick={() => setIsMyTicketDashboard((prev) => !prev)}
          />
        </div>
      </div>

      <ConditionalDisplay condition={isMyTicketDashboard}>
        <DashboardOverview />
      </ConditionalDisplay>
      <ConditionalDisplay condition={!isMyTicketDashboard}>
        <SubmitNewTicket
          firstName={firstName}
          lastName={lastName}
          email={email}
          phone={phone}
          setFirstName={setFirstName}
          setLastName={setLastName}
          setEmail={setEmail}
          setPhone={setPhone}
          inputs={inputs}
          handleInputChange={handleInputChange}
          files={files}
        />
      </ConditionalDisplay>
    </PageContainer>
  )
}

// ========== COMPONENTS ==========

const ProfileCard = () => (
  <div className={styles.profileCard}>
    <div className={styles.profileImage}>
      <div className={styles.avatarPlaceholder}>
        <i className={clsx('pi pi-user', styles.profileImagePlaceholder)} />
      </div>
    </div>
    <div className={styles.profileDetails}>
      <div className={styles.editIcon}>
        <i className={clsx('pi pi-pencil', styles.editUserPencilIcon)} />
      </div>
      <div className={styles.profileHeader}>
        <h2 className={styles.userName}>John Smith</h2>
      </div>
      <div className={styles.profileInfo}>
        <div className={styles.infoRow}>
          <span className={styles.label}>Email:</span>
          <span className={styles.value}>@siu.edu</span>
        </div>
        <div className={styles.infoRow}>
          <span className={styles.label}>Department:</span>
          <span className={styles.value}>Sales</span>
        </div>
        <div className={styles.infoRow}>
          <span className={styles.label}>Office:</span>
          <span className={styles.value}>202A</span>
        </div>
        <div className={styles.infoRow}>
          <span className={styles.label}>Campus:</span>
          <span className={styles.value}>North West</span>
        </div>
        <div className={styles.infoRow}>
          <span className={styles.label}>Phone Number:</span>
          <span className={styles.value}>************</span>
        </div>
        <div className={styles.infoRow}>
          <span className={styles.label}>Join Date:</span>
          <span className={styles.value}>01/08/25</span>
        </div>
      </div>
    </div>
  </div>
)

const ProgramStatusTracker = () => (
  <div className={styles.programStatusTracker}>
    <h2>Program Status Tracker</h2>
    <div className={styles.statusGrid}>
      {[
        { src: ITSMOracleIcon, name: 'Oracle', status: 'Online', className: styles.statusOnline },
        { src: ITSMWorkdayIcon, name: 'Workday', status: 'Online', className: styles.statusOnline },
        { src: ITSMSalesforceIcon, name: 'Salesforce', status: 'In Maintenance', className: styles.statusMaintenance },
        { src: ITSMCanvasIcon, name: 'Canvas', status: 'Online', className: styles.statusOnline }
      ].map((item, i) => (
        <div key={i} className={styles.statusItem}>
          <div className={styles.statusContainer}>
            <Image src={item.src} alt={item.name} width={29} height={29} />
            <div className={styles.statusLabel}>{item.name}</div>
          </div>
          <div className={clsx(styles.statusBadge, item.className)}>{item.status}</div>
        </div>
      ))}
    </div>
  </div>
)

const DraftsInProgressCards = () => (
  <div className={styles.summaryCards}>
    <div className={styles.summaryCard}>
      <Image src={ITSMTicketDrafts} alt="Drafts" />
      <div className={styles.cardContent}>
        <div className={styles.cardLabel}>Drafts</div>
        <div className={styles.cardValue}>20</div>
      </div>
    </div>
    <div className={styles.summaryCard}>
      <Image src={ITSMTicketInProgress} alt="In Progress" />
      <div className={styles.cardContent}>
        <div className={styles.cardLabel}>Tickets In Progress</div>
        <div className={styles.cardValue}>15</div>
      </div>
    </div>
  </div>
)

const CompletedNotificationsCards = () => (
  <div className={styles.summaryCards}>
    <div className={styles.summaryCard}>
      <Image src={ITSMTicketCompleted} alt="Completed" />
      <div className={styles.cardContent}>
        <div className={styles.cardLabel}>Completed Tickets</div>
        <div className={styles.cardValue}>11</div>
      </div>
    </div>
    <div className={styles.summaryCard}>
      <div className={styles.cardIcon} style={{ backgroundColor: 'lightgrey' }}>
        <i className="pi pi-bell" style={{ color: 'grey', fontSize: '2.5rem' }} />
      </div>
      <div className={styles.cardContent}>
        <div className={styles.cardLabel}>Notifications Enabled</div>
        <div className={styles.cardValue}>Yes</div>
      </div>
    </div>
  </div>
)

const ImportantNews = () => (
  <div className={styles.importantNews}>
    <h2>Important News</h2>
    <div className={styles.newsContainer}>
      {[
        { color: '#ff4444', icon: 'pi-exclamation-triangle' },
        { color: '#ff9800', icon: 'pi-wrench' },
        { color: '#ff4444', icon: 'pi-exclamation-triangle' },
        { color: '#8b4513', icon: 'pi-user' }
      ].map((news, i) => (
        <div key={i} className={styles.newsItem}>
          <div className={styles.newsIcon} style={{ backgroundColor: news.color }}>
            <i className={`pi ${news.icon}`} style={{ color: 'white', fontSize: '1rem' }} />
          </div>
          <div className={styles.newsContent}>
            <h3 className={styles.newsTitle}>Maintenance Scheduled 07/16/25</h3>
            <p className={styles.newsDate}>July 13th, 2025</p>
          </div>
          <i className={clsx('pi pi-chevron-right', styles.newsArrow)} />
        </div>
      ))}
    </div>
  </div>
)

const LatestTicketUpdates = () => (
  <div className={styles.latestUpdates}>
    <h2>Latest Ticket Updates</h2>
    <div className={styles.updateItem}>
      <div className={styles.updateHeader}>
        <Image src={ITSMWarningIcon} alt="Warning" width={24} height={24} />
        <span>New Response</span>
      </div>
      <h3 className={styles.updateTitle}>My Login Is Not Working</h3>
      <div className={styles.updateStatus}>Status: In Progress</div>
      <p className={styles.updateDescription}>
        A member of our support team has responded to your ticket; please respond or update the ticket if this issue has been resolved.
      </p>
      <button className={styles.updateButton}>View Ticket</button>
    </div>
  </div>
)

const DashboardOverview = () => (
  <>
    <div className={styles.dashboardGridContainer}>
      <div className={styles.dashboardColumn}>
        <ProfileCard />
        <ProgramStatusTracker />
      </div>
      <div className={styles.dashboardColumn}>
        <DraftsInProgressCards />
        <ImportantNews />
      </div>
      <div className={styles.dashboardColumn}>
        <CompletedNotificationsCards />
        <LatestTicketUpdates />
      </div>
    </div>

    <DataTable value={rows} className="custom-lead" header={tableHeader} style={{ marginTop: '1rem' }}>
      <Column field="id" header="Ticket ID" />
      <Column field="ticketPriority" header="Priority" />
      <Column field="subject" header="Subject" />
      <Column field="category" header="Category" />
      <Column field="subCategory" header="Sub-Category" />
      <Column field="status" header="Status" body={statusTemplate} />
      <Column field="assignedTo" header="Assigned To" body={assignedToTemplate} />
      <Column field="submittedDate" header="Submitted Date" />
      <Column field="lastUpdatedBy" header="Last Updated By" body={lastUpdatedByTemplate} />
      <Column field="lastUpdatedDate" header="Last Updated" />
    </DataTable>
  </>
)

const SubmitTicketInfo = () => (
  <div className={styles.submitTicketBox}>
    <div className={styles.ticketHeaderContent}>
      <div className={styles.ticketImageWrapper}>
        <Image src={ITSMSupportTicket} alt="Support Ticket Illustration" width={447} height={222} />
      </div>
      <div className={styles.ticketTextContent}>
        <h2 className={styles.ticketTitle}>Support Ticket Request</h2>
        <p className={styles.ticketDescription}>
          Need help with a device, application, or access issue? Use this form to submit a request to our IT team. We’ll review and respond
          as soon as possible. Please provide detailed information so we can assist you faster.
        </p>
        <p className={styles.ticketTips}>
          <strong>Tips for Faster Resolutions</strong>
          <p className={styles.ticketTipsDescription}>
            To help our team resolve your issue quickly, be sure to include detailed information in your ticket. Share the steps you’ve
            taken so far, attach relevant screenshots, and provide any additional materials that might assist our staff in troubleshooting.
            The more context we have, the faster we can help!
          </p>
        </p>
      </div>
    </div>
  </div>
)

const SubmitTicketContactInfo = ({ firstName, lastName, email, phone, setFirstName, setLastName, setEmail, setPhone }) => (
  <div className={styles.submitTicketFormContainer}>
    <div className={styles.tableHeader}>Contact Information</div>
    <div className={styles.submitTicketFormBox}>
      <div>
        <div className={styles.submitTicketInfoContainer}>
          <div>
            <label>First Name</label>
            <div className={styles.inputWithIcon}>
              <Image src={PersonIcon} alt="Person Icon" className={styles.inputIcon} />
              <TextInput
                placeholder="Enter your first name"
                theme="metronic"
                className={styles.inputWithPadding}
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
          </div>
          <div>
            <label>Last Name</label>
            <div className={styles.inputWithIcon}>
              <Image src={PersonIcon} alt="Person Icon" className={styles.inputIcon} />
              <TextInput
                placeholder="Enter your last name"
                theme="metronic"
                className={styles.inputWithPadding}
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>
          </div>
        </div>
        <div className={styles.submitTicketInfoContainer}>
          <div>
            <label>Email</label>
            <div className={styles.inputWithIcon}>
              <Image src={MailIcon} alt="Mail Icon" className={styles.inputIcon} />
              <TextInput
                placeholder="Enter your email"
                theme="metronic"
                className={styles.inputWithPadding}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          </div>
          <div>
            <label>Phone Number</label>
            <div className={styles.inputWithIcon}>
              <Image src={PhoneIcon} alt="Phone Icon" className={styles.inputIcon} />
              <TextInput
                placeholder="Enter your phone number"
                theme="metronic"
                className={styles.inputWithPadding}
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

const productOptions = [
  { label: 'Computer & Devices', value: 'Computer & Devices' },
  { label: 'Peripherals', value: 'Peripherals' },
  { label: 'Networking Equipment', value: 'Networking Equipment' },
  { label: 'Software & Licenses', value: 'Software & Licenses' },
  { label: 'Furniture', value: 'Furniture' },
  { label: 'Media & AV Equipment', value: 'Media & AV Equipment' }
]

const computerDevicesOptions = [
  { label: 'Laptop', value: 'Laptop' },
  { label: 'Desktop', value: 'Desktop' },
  { label: 'Tablet', value: 'Tablet' },
  { label: 'Smartphone', value: 'Smartphone' },
  { label: 'Thin Client', value: 'Thin Client' },
  { label: 'eReader', value: 'eReader' }
]

const peripheralsOptions = [
  { label: 'Monitor', value: 'Monitor' },
  { label: 'Keyboard', value: 'Keyboard' },
  { label: 'Mouse', value: 'Mouse' },
  { label: 'Docking Station', value: 'Docking Station' },
  { label: 'Webcam', value: 'Webcam' },
  { label: 'Speakers', value: 'Speakers' },
  { label: 'Microphone', value: 'Microphone' }
]

const networkingEquipmentOptions = [
  { label: 'Router', value: 'Router' },
  { label: 'Switch', value: 'Switch' },
  { label: 'Access Point', value: 'Access Point' },
  { label: 'Network Cable', value: 'Network Cable' },
  { label: 'Modem', value: 'Modem' }
]

const softwareLicensesOptions = [
  { label: 'Operating System', value: 'Operating System' },
  { label: 'Productivity Software', value: 'Productivity Software' },
  { label: 'Security Software', value: 'Security Software' },
  { label: 'Design Software', value: 'Design Software' },
  { label: 'Database License', value: 'Database License' },
  { label: 'Virtualization Software', value: 'Virtualization Software' }
]

const furnitureOptions = [
  { label: 'Desk', value: 'Desk' },
  { label: 'Chair', value: 'Chair' },
  { label: 'Filing Cabinet', value: 'Filing Cabinet' },
  { label: 'Bookshelf', value: 'Bookshelf' },
  { label: 'Conference Table', value: 'Conference Table' },
  { label: 'Couch', value: 'Couch' }
]

const mediaAVEquipmentOptions = [
  { label: 'Projector', value: 'Projector' },
  { label: 'TV', value: 'TV' },
  { label: 'Camera', value: 'Camera' },
  { label: 'Printer', value: 'Printer' },
  { label: 'Scanner', value: 'Scanner' }
]

// Function to get sub-category options based on selected product option
const getSubCategoryOptions = (productOption) => {
  switch (productOption) {
    case 'Computer & Devices':
      return computerDevicesOptions
    case 'Peripherals':
      return peripheralsOptions
    case 'Networking Equipment':
      return networkingEquipmentOptions
    case 'Software & Licenses':
      return softwareLicensesOptions
    case 'Furniture':
      return furnitureOptions
    case 'Media & AV Equipment':
      return mediaAVEquipmentOptions
    default:
      return []
  }
}

const SubmitTicketIssueInfo = ({ inputs, handleInputChange, files }) => {
  const [productOption, setProductOption] = useState('')
  const [subCategoryOption, setSubCategoryOption] = useState('')

  return (
    <div className={styles.submitTicketFormContainer}>
      <div className={styles.tableHeader}>Issue Information</div>
      <div className={styles.submitTicketFormBox}>
        <div className={styles.submitTicketInfoContainer}>
          <div>
            <label>Product / Category</label>
            <SelectInput
              placeholder="Select your product / category"
              theme="metronic"
              options={productOptions}
              value={productOption}
              onChange={(e) => setProductOption(e.target.value)}
            />
          </div>
          <div>
            <label>Sub Category</label>
            <SelectInput
              placeholder="Select your sub category"
              theme="metronic"
              options={getSubCategoryOptions(productOption)}
              value={subCategoryOption}
              onChange={(e) => setSubCategoryOption(e.target.value)}
            />
          </div>
        </div>
        <div className={styles.submitTicketInfoContainer}>
          <div>
            <label>Issue Occured On</label>
            <div className={styles.inputWithIcon}>
              <Image src={CalendarIcon} alt="Calendar Icon" className={styles.trailingIcon} />
              <TextInput placeholder="Select your date" theme="metronic" calendar={true} />
            </div>
          </div>
          <div>
            <label>Subject</label>
            <TextInput placeholder="Enter your subject" theme="metronic" />
          </div>
        </div>
        <div className={styles.submitTicketIssueDescriptionContainer}>
          <div>
            <label>Issue Description</label>
            <TextareaInput autoResize placeholder="Enter your issue description" theme="metronic" />
          </div>
          <div>
            <label>Upload Files</label>
            <AdvancedFileUpload
              fileTypes={['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']}
              onChange={handleInputChange}
              value={inputs?.fileValues}
              files={files?.fileValues}
              name="fileValues"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

const SubmitNewTicket = ({
  firstName,
  lastName,
  email,
  phone,
  setFirstName,
  setLastName,
  setEmail,
  setPhone,
  inputs,
  handleInputChange,
  files
}) => (
  <div className={styles.submitTicketContainer}>
    <SubmitTicketInfo />
    <div className={styles.submitTicketContainer}>
      <SubmitTicketContactInfo
        firstName={firstName}
        lastName={lastName}
        email={email}
        phone={phone}
        setFirstName={setFirstName}
        setLastName={setLastName}
        setEmail={setEmail}
        setPhone={setPhone}
      />
      <SubmitTicketIssueInfo inputs={inputs} handleInputChange={handleInputChange} files={files} />
    </div>
  </div>
)

// ========== UTILITY ==========

const rows = [
  {
    id: '#CV123',
    ticketPriority: 'P1',
    subject: "Login Isn't Working",
    category: 'Software',
    subCategory: 'Account Issues',
    status: 'In Progress',
    assignedTo: 'Tommy Evans',
    submittedDate: '07/16/2025 1:45 PM',
    lastUpdatedBy: 'John Smith',
    lastUpdatedDate: '07/16/2025 3:34 PM'
  },
  {
    id: '#AB123',
    ticketPriority: 'P2',
    subject: 'Laptop Freezing',
    category: 'Hardware',
    subCategory: 'Equipment Failure',
    status: 'Awaiting Response',
    assignedTo: 'Matteo Gobeaux',
    submittedDate: '07/13/2025 1:22 PM',
    lastUpdatedBy: 'Matteo Gobeaux',
    lastUpdatedDate: '07/13/2025 1:22 PM'
  },
  {
    id: '#XY456',
    ticketPriority: 'P3',
    subject: 'Password Reset Request',
    category: 'Software',
    subCategory: 'Account Issues',
    status: 'Submitted',
    assignedTo: 'Sarah Johnson',
    submittedDate: '07/12/2025 10:30 AM',
    lastUpdatedBy: 'Sarah Johnson',
    lastUpdatedDate: '07/12/2025 10:30 AM'
  },
  {
    id: '#QR789',
    ticketPriority: 'P2',
    subject: 'Email Not Syncing',
    category: 'Software',
    subCategory: 'Email Issues',
    status: 'Submitted',
    assignedTo: 'Mike Wilson',
    submittedDate: '07/11/2025 2:15 PM',
    lastUpdatedBy: 'Mike Wilson',
    lastUpdatedDate: '07/11/2025 2:15 PM'
  }
]

const statusTemplate = (rowData) => {
  const isApproved = rowData.status === 'Submitted'
  return (
    <div className={clsx(styles.statusBadge, isApproved ? styles.approveContainer : styles.inprogressContainer)}>
      <span className={isApproved ? styles.approveCircle : styles.inprogressCircle}></span>
      {rowData.status}
    </div>
  )
}

const assignedToTemplate = (rowData) => (
  <div className={styles.userProfileContainer}>
    <div className={clsx(styles.avatarBase, styles.avatarAssigned)}>
      {rowData.assignedTo
        .split(' ')
        .map((name) => name[0])
        .join('')}
    </div>
    <span>{rowData.assignedTo}</span>
  </div>
)

const lastUpdatedByTemplate = (rowData) => (
  <div className={styles.userProfileContainer}>
    <div className={clsx(styles.avatarBase, styles.avatarUpdated)}>
      {rowData.lastUpdatedBy
        .split(' ')
        .map((name) => name[0])
        .join('')}
    </div>
    <span>{rowData.lastUpdatedBy}</span>
  </div>
)

const tableHeader = () => (
  <div className={styles.tableHeader}>
    <span className="text-xl font-bold m-2">Ticket Details</span>
    <span className="p-input-icon-left">
      <i className={clsx('pi pi-search', styles.searchIcon)} />
      <InputText placeholder="Search" className={styles.search} />
    </span>
  </div>
)
