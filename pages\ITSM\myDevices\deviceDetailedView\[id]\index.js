import { <PERSON>Container } from '../../../../../components/UI/Page/PageContainer/PageContainer'
import { useState } from 'react'
import { useRouter } from 'next/router'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import Image from 'next/image'
import Backarrow from '../../../../../svg/metronic/back_metronic.svg'
import BreadCrumbs from '../../../../../components/UI/BreadCrumbs/BreadCrumbs'
import Button from '../../../../../components/UI/Button/Button'
import { ConditionalDisplay } from '../../../../../components/UI/ConditionalDisplay/ConditionalDisplay'

import ITSMTicket from '../../../../../images/submit_new_ticket_icon.png'
import ITSMLaptopImage from '../../../../../images/itsm_laptop_device_image.png' // TODO: Remove this once the API is implemented.

import styles from '../../index.module.css'

const tempInfoObject = {
  deviceName: 'HP 15-da0000',
  assetId: '0001',
  itemId: 'HP-1000',
  category: 'Media',
  supplierId: 'DEL521',
  assetCondition: 'Used',
  assetName: 'Laptop',
  vendor: 'HP',
  serial: '219487',
  SupplierName: 'Dell',
  status: 'Available'
}

const mockSoftwareData = [
  {
    softwareName: 'Adobe Reader',
    installedVersion: '2024.005',
    softwareType: 'PDF Tool',
    publisher: 'Adobe',
    licenseType: 'Per-Device',
    installedBy: '$installedBy',
    installedDate: '$installedBy'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  },
  {
    softwareName: '$softwareName',
    installedVersion: '$installedVersion',
    softwareType: '$softwareType',
    publisher: '$publisher',
    licenseType: '$licenseType',
    installedBy: '$installedBy',
    installedDate: '$installedDate'
  }
]

export default function DeviceDetailedView() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('asset')

  return (
    <PageContainer theme="metronic">
      <div className={styles.myDevicesTopContainer}>
        <Image src={Backarrow} alt="Back" onClick={() => router.back()} className={styles.backArrow} />
        <BreadCrumbs title="My Devices" breadcrumbItems={[{ label: tempInfoObject.deviceName }]} theme="metronic" />
      </div>

      <div>
        <h1 className={styles.ticketTitle} style={{ marginTop: '0' }}>
          {tempInfoObject.deviceName}
        </h1>
      </div>

      <div className={styles.deviceDetailedViewContainer}>
        <div className={styles.topContainer}>
          <div style={{ width: '260px' }} /> {/* This is to ensure layout consistency */}
          <TabSwitcher activeTab={activeTab} onTabChange={setActiveTab} />
          <Button
            style={{ backgroundColor: '#024F7C' }}
            label="Submit Support Ticket"
            theme="metronic"
            width="260px"
            icon={<Image src={ITSMTicket} alt="ITSM Ticket" />}
            onClick={() => router.push('/ITSM?tab=submitNewTicket')}
          />
        </div>
        <div className={styles.middleContainer}>
          <Image src={ITSMLaptopImage} style={{ width: '45%', height: '50%', marginTop: '2rem' }} alt="Laptop" />{' '}
          {/* TODO: Make this dynamic once the API is implemented. */}
          <div className={styles.activeTabContainer}>
            <ConditionalDisplay condition={activeTab === 'asset'} style={{ marginTop: '2rem', flex: 1, alignSelf: 'flex-start' }}>
              <AssetOverview deviceData={tempInfoObject} />
            </ConditionalDisplay>
            <ConditionalDisplay condition={activeTab === 'software'} style={{ marginTop: '2rem', flex: 1, alignSelf: 'flex-start' }}>
              <AvailableSoftware rows={mockSoftwareData} />
            </ConditionalDisplay>
          </div>
        </div>
      </div>
    </PageContainer>
  )
}

const TabSwitcher = ({ activeTab, onTabChange }) => {
  const isAssetOverviewActive = activeTab === 'asset'

  return (
    <div className={styles.tabContainer}>
      <div className={`${styles.tab} ${isAssetOverviewActive ? styles.active : ''}`} onClick={() => onTabChange('asset')}>
        Asset Overview
      </div>
      <div className={`${styles.tab} ${!isAssetOverviewActive ? styles.active : ''}`} onClick={() => onTabChange('software')}>
        Available Software
      </div>
    </div>
  )
}

const AssetOverview = ({ deviceData }) => {
  return (
    <div className={styles.assetOverviewContainer}>
      <div className={styles.assetInfoColumn}>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Asset ID (auto-generated)</div>
          <div className={styles.assetInfoValue}>{deviceData.assetId}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Item ID</div>
          <div className={styles.assetInfoValue}>{deviceData.itemId}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Category</div>
          <div className={styles.assetInfoValue}>{deviceData.category}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Supplier ID</div>
          <div className={styles.assetInfoValue}>{deviceData.supplierId}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Asset Condition</div>
          <div className={styles.assetInfoValue}>{deviceData.assetCondition}</div>
        </div>
      </div>

      <div className={styles.assetInfoColumn}>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Asset Name</div>
          <div className={styles.assetInfoValue}>{deviceData.assetName}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Vendor</div>
          <div className={styles.assetInfoValue}>{deviceData.vendor}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Serial #</div>
          <div className={styles.assetInfoValue}>{deviceData.serial}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Supplier Name</div>
          <div className={styles.assetInfoValue}>{deviceData.SupplierName}</div>
        </div>
        <div className={styles.assetInfoItem}>
          <div className={styles.assetInfoTitle}>Status</div>
          <div className={styles.assetInfoValue}>{deviceData.status}</div>
        </div>
      </div>
    </div>
  )
}

const AvailableSoftware = ({ rows }) => {
  return (
    <DataTable value={rows} className="custom-lead">
      <Column field="softwareName" header="Software Name" headerStyle={{ width: '15%' }} />
      <Column field="installedVersion" header="Installed Version" headerStyle={{ width: '15%' }} />
      <Column field="softwareType" header="Software Type" headerStyle={{ width: '15%' }} />
      <Column field="publisher" header="Publisher" headerStyle={{ width: '12%' }} />
      <Column field="licenseType" header="License Type" headerStyle={{ width: '12%' }} />
      <Column field="installedBy" header="Installed By" headerStyle={{ width: '14%' }} />
      <Column field="installedDate" header="Installed Date" headerStyle={{ width: '14%' }} />
    </DataTable>
  )
}
